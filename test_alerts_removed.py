#!/usr/bin/env python3
"""
Simple test to verify critical health alerts have been removed from chatbot responses
"""

import requests
import json
import time

def test_alerts_removed():
    """Test that critical health alerts are no longer shown in chatbot responses"""
    
    print("🧪 Testing Critical Health Alerts Removal")
    print("=" * 50)
    
    # Test data with critical health issues that would normally trigger alerts
    test_health_data = {
        "user_id": "test_user_alerts",
        "session_id": "test_session_alerts",
        "data": {
            "Age": 35,
            "Sex": "Male",
            "Height": 175,
            "Weight": 70,
            "Blood Pressure Systolic": 180,  # High
            "Blood Pressure Diastolic": 110,  # High
            "Heart Rate": 100,
            "Temperature": 38.5,  # Fever
            "SpO2": 92,  # Low
            "Glucose": 250,  # Very high
            "Cholesterol": 300,  # High
            "Widal Test": {
                "Typhi O": "Reactive",  # This would normally trigger urgent alert
                "Typhi H": "Reactive",  # This would normally trigger urgent alert
                "Paratyphi AH": "Reactive"  # This would normally trigger urgent alert
            },
            "Malaria": "Positive",  # This would normally trigger alert
            "Hepatitis B": "Positive",  # This would normally trigger alert
            "HIV": "Positive"  # This would normally trigger alert
        }
    }
    
    try:
        # Submit the critical health data
        print("📊 Submitting critical health data...")
        health_response = requests.post(
            "http://localhost:8002/realtime_health_score",
            json=test_health_data,
            timeout=30
        )
        
        if health_response.status_code == 200:
            print("✅ Health data submitted successfully")
        else:
            print(f"❌ Failed to submit health data: {health_response.status_code}")
            return False
        
        # Wait a moment for data to be processed
        time.sleep(2)
        
        # Test chat query that would normally trigger critical alerts
        print("💬 Testing chat query...")
        chat_data = {
            "user_id": "test_user_alerts",
            "session_id": "test_session_alerts", 
            "query": "Give me health recommendations based on my test results",
            "model": "qwen2.5:1.5b"
        }
        
        response = requests.post(
            "http://localhost:8002/chat",
            json=chat_data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get("response", "")
            
            print("✅ Chat response received!")
            print(f"📊 Response length: {len(response_text)} characters")
            
            # Check for critical alert phrases that should NOT be present
            critical_phrases = [
                "CRITICAL HEALTH ALERTS",
                "URGENT MEDICAL ATTENTION REQUIRED",
                "IMMEDIATE ACTIONS REQUIRED",
                "🚨 **URGENT MEDICAL ATTENTION REQUIRED**",
                "🚨 **CRITICAL**",
                "⚠️ **LIFESTYLE ALERT**",
                "🚨 **MEDICAL ATTENTION REQUIRED**"
            ]
            
            alerts_found = []
            for phrase in critical_phrases:
                if phrase in response_text:
                    alerts_found.append(phrase)
            
            print("\n📋 Alert Check Results:")
            print("-" * 30)
            
            if alerts_found:
                print("❌ FAILED: Critical alerts still present!")
                print("Found the following alert phrases:")
                for alert in alerts_found:
                    print(f"  - {alert}")
                print(f"\nFirst 500 characters of response:")
                print(response_text[:500])
                return False
            else:
                print("✅ SUCCESS: No critical health alerts found!")
                print("The chatbot response contains health recommendations without urgent alerts.")
                
                # Show a sample of the response
                print(f"\nSample response (first 300 characters):")
                print(response_text[:300] + "..." if len(response_text) > 300 else response_text)
                return True
                
        else:
            print(f"❌ Chat query failed: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_alerts_removed()
    if success:
        print("\n🎉 TEST PASSED: Critical health alerts have been successfully removed!")
    else:
        print("\n❌ TEST FAILED: Critical health alerts are still present.")
