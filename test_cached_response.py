#!/usr/bin/env python3
"""
Test using cached response to check duplication
"""

import requests

def test_cached_response():
    """Test with a user that should have cached response"""
    
    print("🧪 Testing Cached Response for Duplication")
    print("=" * 50)
    
    # Use the same user from cache performance test
    query_data = {
        "user_id": "cache_test_user",
        "session_id": "cache_test_session",
        "query": "What health recommendations do you have for me?",
        "model": "qwen2.5:1.5b"
    }
    
    print("💬 Testing cached recommendation query...")
    try:
        response = requests.post("http://127.0.0.1:8000/query", json=query_data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get("response", "")
            
            print("✅ Got cached response!")
            print(f"📊 Response length: {len(response_text)} characters")
            
            # Check for duplications
            critical_count = response_text.count("CRITICAL HEALTH ALERTS")
            urgent_count = response_text.count("URGENT MEDICAL ATTENTION")
            
            print(f"\n🔍 Duplication Check:")
            print(f"  'CRITICAL HEALTH ALERTS': {critical_count}")
            print(f"  'URGENT MEDICAL ATTENTION': {urgent_count}")
            
            if critical_count <= 1 and urgent_count <= 1:
                print("✅ No duplications in cached response!")
            else:
                print("⚠️ Duplications found in cached response")
            
            # Show response
            print(f"\n📄 Full Response:")
            print("=" * 50)
            print(response_text)
            print("=" * 50)
            
        else:
            print(f"❌ Failed: {response.status_code}")
            print(response.text)
            
    except requests.exceptions.Timeout:
        print("⏰ Even cached response timed out - there may be a server issue")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_cached_response()
