#!/usr/bin/env python3
"""
Test the improved response that combines critical alerts with LLM intelligence
"""

import requests
import json

def test_improved_response():
    """Test the new response format that keeps LLM intelligence"""
    
    print("🧪 Testing Improved Response Format")
    print("=" * 60)
    print("This test shows how the response now combines:")
    print("✅ Critical health alerts (structured)")
    print("✅ LLM intelligence (vector store + health data)")
    print("❌ No duplication")
    print("=" * 60)
    
    # Submit critical health data
    health_data = {
        "user_id": "improved_test_user",
        "health_data": {
            "Glucose": 85.5,
            "Hepatitis B": "Negative",
            "Systolic": 120.0,
            "Diastolic": 80.0,
            "Waist Circumference": 85.0,
            "Hiv": "Negative",
            "Fev": 95.0,
            "Temperature": 37.0,
            "Ecg": 75.0,
            "Spo2": 98.0,
            "Weight": 70.5,
            "Widal Test": {
                "Typhi O": "Reactive",
                "Typhi H": "Reactive",
                "Paratyphi AH": "Non-Reactive",
                "Paratyphi BH": "Non-Reactive"
            },
            "Malaria": "Positive",
            "Kidney": 1.0,
            "Lipid": 180.0,
            "Liver": 25.0,
            "Respiratory": 16.0,
            # Critical lifestyle factors
            "Exercise": 0,
            "Diet": 1,
            "Sleep": 4,
            "Stress": 5,
            "Hydration": 2,
            "Smoking": 10,
            "Alcohol": 5,
            "Social Connection": 1
        }
    }
    
    print("📊 Submitting critical health data...")
    response = requests.post("http://127.0.0.1:8000/realtime-health-score", json=health_data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Health data submitted successfully")
        print(f"📊 Score: {result.get('Total Score', 'Unknown')}")
        print(f"🏥 Status: {result.get('Health Status', 'Unknown')}")
    else:
        print(f"❌ Failed to submit health data: {response.status_code}")
        return False
    
    # Test the recommendation query
    query_data = {
        "user_id": "improved_test_user",
        "session_id": "improved_test_session",
        "query": "Based on my health score, what recommendations do you have for me?",
        "model": "qwen2.5:1.5b"
    }
    
    print("\n💬 Testing improved recommendation response...")
    print("⏳ This may take a moment for the LLM to process...")
    
    try:
        response = requests.post("http://127.0.0.1:8000/query", json=query_data, timeout=90)
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get("response", "")
            tools_used = result.get("tools_used", [])
            
            print("✅ Response received!")
            print(f"📊 Response length: {len(response_text)} characters")
            print(f"🔧 Tools used: {tools_used}")
            
            # Analyze the response structure
            print(f"\n📋 Response Structure Analysis:")
            
            has_critical_alerts = "CRITICAL HEALTH ALERTS" in response_text
            has_urgent_medical = "URGENT MEDICAL ATTENTION" in response_text
            has_widal_info = "Widal Test" in response_text and "reactive" in response_text.lower()
            has_malaria_info = "Malaria" in response_text and "Positive" in response_text
            has_lifestyle_info = "lifestyle" in response_text.lower()
            has_llm_insights = len(response_text) > 1000  # LLM responses are typically longer
            
            # Check for duplications
            critical_count = response_text.count("CRITICAL HEALTH ALERTS")
            urgent_count = response_text.count("URGENT MEDICAL ATTENTION")
            widal_count = response_text.count("Widal Test shows")
            
            print(f"✅ Critical alerts present: {has_critical_alerts}")
            print(f"✅ Urgent medical attention: {has_urgent_medical}")
            print(f"✅ Widal Test information: {has_widal_info}")
            print(f"✅ Malaria information: {has_malaria_info}")
            print(f"✅ Lifestyle information: {has_lifestyle_info}")
            print(f"✅ LLM insights included: {has_llm_insights}")
            
            print(f"\n🔍 Duplication Check:")
            print(f"  'CRITICAL HEALTH ALERTS': {critical_count} (should be 1)")
            print(f"  'URGENT MEDICAL ATTENTION': {urgent_count} (should be ≤2)")
            print(f"  'Widal Test shows': {widal_count} (should be ≤1)")
            
            duplications = (critical_count > 1) + (urgent_count > 2) + (widal_count > 1)
            
            if duplications == 0:
                print("🎉 SUCCESS: No significant duplications detected!")
            else:
                print(f"⚠️ WARNING: {duplications} potential duplications found")
            
            # Show the response structure
            print(f"\n📄 IMPROVED RESPONSE FORMAT:")
            print("=" * 80)
            
            # Show first part (critical alerts)
            if len(response_text) > 500:
                print(response_text[:500])
                print("\n[... MIDDLE CONTENT ...]\n")
                print(response_text[-300:])
            else:
                print(response_text)
            
            print("=" * 80)
            
            # Summary
            if has_critical_alerts and has_llm_insights and duplications == 0:
                print("\n🎉 PERFECT! The response now includes:")
                print("  ✅ Critical health alerts at the top")
                print("  ✅ LLM intelligence with vector store context")
                print("  ✅ Personalized health recommendations")
                print("  ✅ No duplication issues")
                return True
            else:
                print("\n⚠️ PARTIAL SUCCESS: Some improvements needed")
                return False
                
        else:
            print(f"❌ Query failed: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Query timed out - LLM processing may be taking longer than expected")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_improved_response()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 IMPROVED RESPONSE TEST PASSED!")
        print("The response now perfectly combines critical alerts with LLM intelligence!")
    else:
        print("⚠️ IMPROVED RESPONSE TEST NEEDS REFINEMENT")
        print("The system is working but may need further optimization.")
