#!/usr/bin/env python3
"""
Verify that the critical health alerts code has been properly disabled
"""

def verify_code_changes():
    """Verify that critical health alerts have been disabled in the code"""
    
    print("🔍 Verifying Code Changes")
    print("=" * 40)
    
    try:
        # Read the agent_server.py file
        with open('agent_server.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check that critical alert generation is commented out
        checks = [
            {
                'description': 'Critical alerts initialization is disabled',
                'pattern': 'critical_alerts = []',
                'should_exist': True
            },
            {
                'description': 'Urgent recommendations initialization is disabled', 
                'pattern': 'urgent_recommendations = []',
                'should_exist': True
            },
            {
                'description': 'Widal Test alerts are commented out',
                'pattern': '# if "Widal Test" in raw_data',
                'should_exist': True
            },
            {
                'description': 'Critical health score alerts are commented out',
                'pattern': '# if rt_score < 60:',
                'should_exist': True
            },
            {
                'description': 'Critical section creation is commented out',
                'pattern': '# if critical_alerts or urgent_recommendations:',
                'should_exist': True
            },
            {
                'description': 'URGENT MEDICAL ATTENTION text is commented out',
                'pattern': '#         critical_alerts.append(f"🚨 **URGENT MEDICAL ATTENTION REQUIRED**',
                'should_exist': True
            },
            {
                'description': 'CRITICAL HEALTH ALERTS header is commented out',
                'pattern': '#     critical_section = "🚨 **CRITICAL HEALTH ALERTS** 🚨\\n\\n"',
                'should_exist': True
            }
        ]
        
        results = []
        for check in checks:
            found = check['pattern'] in content
            results.append({
                'description': check['description'],
                'expected': check['should_exist'],
                'found': found,
                'passed': found == check['should_exist']
            })
        
        # Print results
        print("📋 Code Change Verification Results:")
        print("-" * 40)
        
        all_passed = True
        for result in results:
            status = "✅ PASS" if result['passed'] else "❌ FAIL"
            print(f"{status}: {result['description']}")
            if not result['passed']:
                all_passed = False
                expected = "should exist" if result['expected'] else "should not exist"
                found = "found" if result['found'] else "not found"
                print(f"    Expected: {expected}, Actual: {found}")
        
        print("\n" + "=" * 40)
        
        if all_passed:
            print("🎉 SUCCESS: All critical health alert code has been properly disabled!")
            print("\nKey changes verified:")
            print("- Critical alerts arrays are empty")
            print("- Widal Test alert generation is commented out")
            print("- Critical health score alerts are commented out") 
            print("- Critical section creation is commented out")
            print("- All urgent medical attention messages are disabled")
            return True
        else:
            print("❌ FAILURE: Some critical health alert code is still active!")
            return False
            
    except FileNotFoundError:
        print("❌ Error: agent_server.py file not found")
        return False
    except Exception as e:
        print(f"❌ Error reading file: {str(e)}")
        return False

def verify_agent_app_changes():
    """Verify that critical health alerts have been disabled in Agent Server app"""
    
    print("\n🔍 Verifying Agent App Changes")
    print("=" * 40)
    
    try:
        # Read the Agent Server/agent_app.py file
        with open('Agent Server/agent_app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check that emergency alerts are commented out
        checks = [
            {
                'description': 'Emergency level check is commented out',
                'pattern': '# if result.get("urgency_level") == "emergency":',
                'should_exist': True
            },
            {
                'description': 'URGENT MEDICAL ATTENTION NEEDED is commented out',
                'pattern': '#     response += "🚨 **URGENT MEDICAL ATTENTION NEEDED** 🚨\\n\\n"',
                'should_exist': True
            }
        ]
        
        results = []
        for check in checks:
            found = check['pattern'] in content
            results.append({
                'description': check['description'],
                'expected': check['should_exist'],
                'found': found,
                'passed': found == check['should_exist']
            })
        
        # Print results
        print("📋 Agent App Change Verification Results:")
        print("-" * 40)
        
        all_passed = True
        for result in results:
            status = "✅ PASS" if result['passed'] else "❌ FAIL"
            print(f"{status}: {result['description']}")
            if not result['passed']:
                all_passed = False
        
        return all_passed
            
    except FileNotFoundError:
        print("❌ Error: Agent Server/agent_app.py file not found")
        return False
    except Exception as e:
        print(f"❌ Error reading file: {str(e)}")
        return False

if __name__ == "__main__":
    success1 = verify_code_changes()
    success2 = verify_agent_app_changes()
    
    if success1 and success2:
        print("\n🎉 OVERALL SUCCESS: Critical health alerts have been successfully removed from both files!")
        print("\nThe chatbot will no longer display:")
        print("- 🚨 CRITICAL HEALTH ALERTS")
        print("- 🚨 URGENT MEDICAL ATTENTION REQUIRED")
        print("- ⚠️ LIFESTYLE ALERT")
        print("- IMMEDIATE ACTIONS REQUIRED")
        print("\nInstead, it will provide friendly health recommendations without urgent alerts.")
    else:
        print("\n❌ OVERALL FAILURE: Some critical health alerts may still be present.")
