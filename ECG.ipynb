import os

# Base directory
base_dir = '/Users/<USER>/Desktop/MRI/ECG_DATA'

# Dictionary to store counts
file_counts = {}

# Walk through the directory structure
for dirname, _, filenames in os.walk(base_dir):
    category = dirname.split("/")[-1]  # Get category name
    if category:  
        file_counts[category] = len(filenames)

# Display file counts
for category, count in file_counts.items():
    print(f"{category}: {count} files")

# Separate counts for train and test categories
train_categories = ["ECG Images of Myocardial Infarction Patients",
                    "ECG Images of Patient that have History of MI",
                    "ECG Images of Patient that have abnormal heartbeat",
                    "Normal Person ECG Images"]

test_categories = ["Test " + cat for cat in train_categories]

train_counts = [file_counts.get(cat, 0) for cat in train_categories]
test_counts = [file_counts.get(cat, 0) for cat in test_categories]


import tensorflow as tf
from tensorflow.keras.preprocessing.image import ImageDataGenerator # type: ignore
from tensorflow.keras.models import Sequential # type: ignore
from tensorflow.keras.layers import Conv2D, MaxPooling2D, <PERSON><PERSON>, <PERSON><PERSON>, Dropout # type: ignore
from tensorflow.keras.optimizers import Adam # type: ignore
import os

# Check if GPU is available
print("GPU Available:", tf.config.list_physical_devices('GPU'))

# Define paths to the dataset directories
data_dir = '/Users/<USER>/Desktop/MRI/ECG_DATA'
train_dir = os.path.join(data_dir, 'train')
test_dir = os.path.join(data_dir, 'test')

# Image data generators for training and testing
train_datagen = ImageDataGenerator(
    rescale=1.0/255,
    shear_range=0.2,
    zoom_range=0.2,
    horizontal_flip=True
)
test_datagen = ImageDataGenerator(rescale=1.0/255)

# Load the data from directories
train_generator = train_datagen.flow_from_directory(
    train_dir,
    target_size=(150, 150),
    batch_size=32,
    class_mode='categorical'
)

test_generator = test_datagen.flow_from_directory(
    test_dir,
    target_size=(150, 150),
    batch_size=32,
    class_mode='categorical'
)

# Build the CNN model
model = Sequential([
    Conv2D(32, (3, 3), activation='relu', input_shape=(150, 150, 3)),
    MaxPooling2D(pool_size=(2, 2)),
    Conv2D(64, (3, 3), activation='relu'),
    MaxPooling2D(pool_size=(2, 2)),
    Conv2D(128, (3, 3), activation='relu'),
    MaxPooling2D(pool_size=(2, 2)),
    Flatten(),
    Dense(512, activation='relu'),
    Dropout(0.5),
    Dense(4, activation='softmax')
])

# Compile the model
model.compile(
    optimizer=Adam(learning_rate=0.001),
    loss='categorical_crossentropy',
    metrics=['accuracy']
)

# Train the model
history = model.fit(
    train_generator,
    # steps_per_epoch=train_generator.samples // train_generator.batch_size,
    epochs=20,
    validation_data=test_generator,
    # validation_steps=test_generator.samples // test_generator.batch_size
)

# Evaluate the model
loss, accuracy = model.evaluate(test_generator)
print(f'Test accuracy: {accuracy * 100:.2f}%')


# Save the trained model
model.save('ecg_model.h5')