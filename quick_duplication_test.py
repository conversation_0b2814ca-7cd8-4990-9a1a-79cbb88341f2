#!/usr/bin/env python3
"""
Quick test to verify duplication fix
"""

import requests
import json

def quick_test():
    """Quick test for duplication fix"""
    
    print("🧪 Quick Duplication Fix Test")
    print("=" * 40)
    
    # Submit health data with critical issues
    health_data = {
        "user_id": "quick_test",
        "health_data": {
            "Glucose": 85.5,
            "Widal Test": {
                "Typhi O": "Reactive",
                "Typhi H": "Reactive"
            },
            "Malaria": "Positive",
            "Exercise": 0,
            "Diet": 1,
            "Sleep": 4,
            "Stress": 5
        }
    }
    
    print("📊 Submitting critical health data...")
    response = requests.post("http://127.0.0.1:8000/realtime-health-score", json=health_data)
    
    if response.status_code == 200:
        print("✅ Health data submitted")
    else:
        print(f"❌ Failed: {response.status_code}")
        return
    
    # Test recommendation query
    query_data = {
        "user_id": "quick_test",
        "session_id": "quick_session",
        "query": "What recommendations do you have?",
        "model": "qwen2.5:1.5b"
    }
    
    print("💬 Testing recommendation query...")
    try:
        response = requests.post("http://127.0.0.1:8000/query", json=query_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get("response", "")
            
            # Quick duplication check
            critical_count = response_text.count("CRITICAL HEALTH ALERTS")
            urgent_count = response_text.count("URGENT MEDICAL ATTENTION")
            widal_count = response_text.count("Widal Test shows")
            
            print(f"📊 Quick Analysis:")
            print(f"  'CRITICAL HEALTH ALERTS': {critical_count} occurrences")
            print(f"  'URGENT MEDICAL ATTENTION': {urgent_count} occurrences")
            print(f"  'Widal Test shows': {widal_count} occurrences")
            
            if critical_count <= 1 and urgent_count <= 1 and widal_count <= 1:
                print("✅ SUCCESS: No duplications detected!")
            else:
                print("⚠️ ISSUE: Duplications still present")
            
            # Show first 300 characters
            print(f"\n📄 Response Preview:")
            print("-" * 40)
            print(response_text[:300] + "..." if len(response_text) > 300 else response_text)
            print("-" * 40)
            
        else:
            print(f"❌ Query failed: {response.status_code}")
            
    except requests.exceptions.Timeout:
        print("⏰ Query timed out")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    quick_test()
