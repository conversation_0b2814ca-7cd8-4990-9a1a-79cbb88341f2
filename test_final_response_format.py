#!/usr/bin/env python3
"""
Test the final response format - critical alerts + LLM only
"""

import requests

def test_final_format():
    """Test the final clean response format"""
    
    print("🧪 Testing Final Response Format")
    print("=" * 50)
    print("Expected format:")
    print("✅ Critical health alerts (when present)")
    print("✅ LLM response with vector store + health data")
    print("❌ NO structured recommendations")
    print("❌ NO device recommendations")
    print("❌ NO duplications")
    print("=" * 50)
    
    # Submit critical health data
    health_data = {
        "user_id": "final_test_user",
        "health_data": {
            "Glucose": 85.5,
            "Widal Test": {
                "Typhi O": "Reactive",
                "Typhi H": "Reactive",
                "Paratyphi AH": "Reactive"
            },
            "Malaria": "Negative",
            "Exercise": 0,
            "Diet": 1,
            "Sleep": 4,
            "Stress": 5,
            "Hydration": 2,
            "Smoking": 10,
            "Alcohol": 5
        }
    }
    
    print("📊 Submitting critical health data...")
    response = requests.post("http://127.0.0.1:8000/realtime-health-score", json=health_data)
    
    if response.status_code == 200:
        print("✅ Health data submitted")
    else:
        print(f"❌ Failed: {response.status_code}")
        return
    
    # Test recommendation query
    query_data = {
        "user_id": "final_test_user",
        "session_id": "final_test_session",
        "query": "What recommendations do you have based on my health?",
        "model": "qwen2.5:1.5b"
    }
    
    print("💬 Testing final response format...")
    try:
        response = requests.post("http://127.0.0.1:8000/query", json=query_data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get("response", "")
            
            print("✅ Response received!")
            print(f"📊 Length: {len(response_text)} characters")
            
            # Check what's included
            has_critical_alerts = "CRITICAL HEALTH ALERTS" in response_text
            has_structured_recs = "Real-Time Health Score Recommendations" in response_text
            has_device_recs = "TurboMedics" in response_text
            has_priority_focus = "Priority: Focus on getting medical attention" in response_text
            
            print(f"\n📋 Content Analysis:")
            print(f"✅ Critical alerts: {has_critical_alerts}")
            print(f"❌ Structured recommendations: {has_structured_recs} (should be False)")
            print(f"❌ Device recommendations: {has_device_recs} (should be False)")
            print(f"❌ Priority focus message: {has_priority_focus} (should be False)")
            
            # Show the response
            print(f"\n📄 FINAL RESPONSE FORMAT:")
            print("=" * 60)
            print(response_text)
            print("=" * 60)
            
            # Success criteria
            success = (has_critical_alerts and 
                      not has_structured_recs and 
                      not has_device_recs and 
                      not has_priority_focus)
            
            if success:
                print("\n🎉 PERFECT! Final format achieved:")
                print("  ✅ Critical alerts present")
                print("  ✅ No structured recommendations")
                print("  ✅ No device recommendations")
                print("  ✅ Clean LLM response only")
            else:
                print("\n⚠️ Format needs refinement:")
                if has_structured_recs:
                    print("  ❌ Still showing structured recommendations")
                if has_device_recs:
                    print("  ❌ Still showing device recommendations")
                if has_priority_focus:
                    print("  ❌ Still showing priority focus message")
            
        else:
            print(f"❌ Query failed: {response.status_code}")
            
    except requests.exceptions.Timeout:
        print("⏰ Query timed out")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_final_format()
