#!/usr/bin/env python3
"""
Test to verify the duplication fix for critical health alerts
"""

import requests
import json

def test_duplication_fix():
    """Test that critical health alerts are not duplicated"""
    
    print("🧪 Testing Duplication Fix for Critical Health Alerts")
    print("=" * 60)
    
    # Submit critical health data with reactive Widal Test and positive Malaria
    health_data = {
        "user_id": "duplication_test_user",
        "health_data": {
            "Glucose": 85.5,
            "Hepatitis B": "Negative",
            "Systolic": 120.0,
            "Diastolic": 80.0,
            "Waist Circumference": 85.0,
            "Hiv": "Negative",
            "Fev": 95.0,
            "Temperature": 37.0,
            "Ecg": 75.0,
            "Spo2": 98.0,
            "Weight": 70.5,
            "Widal Test": {
                "Typhi O": "Reactive",
                "Typhi H": "Reactive",
                "Paratyphi AH": "Non-Reactive",
                "Paratyphi BH": "Non-Reactive"
            },
            "Malaria": "Positive",  # This should trigger critical alert
            "Kidney": 1.0,
            "Lipid": 180.0,
            "Liver": 25.0,
            "Respiratory": 16.0,
            # Critical lifestyle factors
            "Exercise": 0,
            "Diet": 1,
            "Sleep": 4,
            "Stress": 5,
            "Hydration": 2,
            "Smoking": 10,
            "Alcohol": 5,
            "Social Connection": 1
        }
    }
    
    print("📊 Submitting critical health data...")
    response = requests.post("http://127.0.0.1:8000/realtime-health-score", json=health_data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Health data submitted successfully")
        print(f"📊 Score: {result.get('Total Score', 'Unknown')}")
    else:
        print(f"❌ Failed to submit health data: {response.status_code}")
        return False
    
    # Now test the recommendation query
    query_data = {
        "user_id": "duplication_test_user",
        "session_id": "duplication_test_session",
        "query": "Based on my health score, what recommendations do you have?",
        "model": "qwen2.5:1.5b"
    }
    
    print("\n💬 Testing recommendation query...")
    response = requests.post("http://127.0.0.1:8000/query", json=query_data, timeout=60)
    
    if response.status_code == 200:
        result = response.json()
        response_text = result.get("response", "")
        
        print("📝 Response Analysis:")
        print("=" * 50)
        
        # Count occurrences of key phrases that might be duplicated
        duplication_checks = {
            "CRITICAL HEALTH ALERTS": response_text.count("CRITICAL HEALTH ALERTS"),
            "URGENT MEDICAL ATTENTION REQUIRED": response_text.count("URGENT MEDICAL ATTENTION REQUIRED"),
            "Widal Test shows": response_text.count("Widal Test shows"),
            "reactive antibodies": response_text.count("reactive antibodies"),
            "typhoid infection": response_text.count("typhoid infection"),
            "Positive results detected": response_text.count("Positive results detected"),
            "Malaria": response_text.count("Malaria"),
            "IMMEDIATE ACTIONS REQUIRED": response_text.count("IMMEDIATE ACTIONS REQUIRED"),
            "Contact your healthcare provider": response_text.count("Contact your healthcare provider"),
            "Do not self-medicate": response_text.count("Do not self-medicate")
        }
        
        print("🔍 Duplication Check Results:")
        duplications_found = 0
        for phrase, count in duplication_checks.items():
            if count > 1:
                print(f"❌ '{phrase}': {count} occurrences (DUPLICATED)")
                duplications_found += 1
            elif count == 1:
                print(f"✅ '{phrase}': {count} occurrence (GOOD)")
            else:
                print(f"⚠️ '{phrase}': {count} occurrences (MISSING)")
        
        print(f"\n📊 Summary:")
        print(f"Total duplications found: {duplications_found}")
        print(f"Response length: {len(response_text)} characters")
        
        # Show a preview of the response
        print(f"\n📄 Response Preview (first 500 chars):")
        print("-" * 50)
        print(response_text[:500] + "..." if len(response_text) > 500 else response_text)
        print("-" * 50)
        
        if duplications_found == 0:
            print("\n🎉 SUCCESS: No duplications found! The fix is working.")
            return True
        else:
            print(f"\n⚠️ ISSUE: {duplications_found} duplications still present.")
            return False
            
    else:
        print(f"❌ Query failed: {response.status_code}")
        print(f"Error: {response.text}")
        return False

if __name__ == "__main__":
    success = test_duplication_fix()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 DUPLICATION FIX TEST PASSED!")
        print("Critical health alerts are now properly formatted without duplication.")
    else:
        print("❌ DUPLICATION FIX TEST FAILED!")
        print("Further investigation needed to eliminate duplications.")
