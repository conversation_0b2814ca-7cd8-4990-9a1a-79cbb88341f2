# Real-Time Health Score API Documentation

## Overview
The Real-Time Health Score is an advanced AI-powered health assessment system that provides comprehensive, dynamic health scoring by combining vital signs, lifestyle factors, and test results into a single holistic health metric. The system uses evidence-based weighting algorithms to deliver personalized health insights, trend analysis, and actionable recommendations for optimal health management.

## Base URL
The endpoint is available at: `/realtime-health-score`

## Core Capabilities
- **Holistic Health Assessment**: Combines vitals, lifestyle, and test results with weighted scoring
- **Dynamic Scoring Engine**: Real-time calculation with evidence-based category weighting
- **Comprehensive Analysis**: Detailed breakdown by health categories and individual metrics
- **Personalized Recommendations**: AI-generated improvement tips based on specific health data
- **Trend Monitoring**: Historical data tracking and significant change detection
- **Risk Assessment**: Automated alerts for concerning health metrics
- **Medical Integration**: Support for detailed test results including Widal Test antibody panels

---

## API Endpoint

### Generate Real-Time Health Score
**Endpoint:** `POST /realtime-health-score`

**Purpose:** Generate a comprehensive health score that combines vital signs, lifestyle factors, and test results into a dynamic, weighted health assessment

**Request Payload:**
```json
{
  "user_id": "patient_123",
  "health_data": {
    "Glucose": 85.5,
    "Hepatitis B": "Negative",
    "Systolic": 120,
    "Diastolic": 80,
    "Waist Circumference": 85.0,
    "Hiv": "Negative",
    "Fev": 95.0,
    "Temperature": 37.0,
    "Ecg": 75,
    "Spo2": 98,
    "Weight": 22.1,
    "Widal Test": {
      "Typhi O": "Non-Reactive",
      "Typhi H": "Non-Reactive",
      "Paratyphi AH": "Non-Reactive",
      "Paratyphi BH": "Non-Reactive"
    },
    "Malaria": "Negative",
    "Kidney": 1.0,
    "Lipid": 180.0,
    "Liver": 25.0,
    "Respiratory": 16,
    "Exercise": 4,
    "Diet": 4,
    "Sleep": 8,
    "Stress": 2,
    "Hydration": 10,
    "Smoking": 0,
    "Alcohol": 1,
    "Social Connection": 4
  }
}
```

**Field Requirements:**

**Vital Signs (40% weight):**
- `Systolic`: Systolic blood pressure in mmHg (optional, integer, range: 90-120)
- `Diastolic`: Diastolic blood pressure in mmHg (optional, integer, range: 60-80)
- `Ecg`: Heart rate in BPM (optional, float, range: 60-100)
- `Spo2`: Blood oxygen saturation in % (optional, float, range: 95-100)
- `Temperature`: Body temperature in °C (optional, float, range: 36.5-37.5)
- `Weight`: BMI value (optional, float, range: 18.5-24.9)
- `Waist Circumference`: Waist measurement in cm (optional, float, range: 80-94)
- `Fev`: Forced expiratory volume in % (optional, float, range: 80-120)
- `Respiratory`: Respiratory rate in breaths/min (optional, float, range: 12-20)

**Test Results (25% weight):**
- `Glucose`: Blood glucose in mg/dL (optional, float, range: 70-100)
- `Malaria`: Test result (optional, string: "Negative"/"Positive"/"Unknown")
- `Widal Test`: Typhoid test results (optional, object or string)
- `Hepatitis B`: Test result (optional, string: "Negative"/"Positive"/"Unknown")
- `Hiv`: Test result (optional, string: "Negative"/"Positive"/"Unknown")
- `Kidney`: Creatinine level in mg/dL (optional, float, range: 0.6-1.2)
- `Lipid`: Total cholesterol in mg/dL (optional, float, range: 0-200)
- `Liver`: Liver enzyme level in U/L (optional, float, range: 7-56)

**Lifestyle Factors (35% weight):**
- `Exercise`: Exercise frequency in days/week (optional, integer, range: 3-5)
- `Diet`: Diet quality score 1-5 (optional, integer, range: 3-5)
- `Sleep`: Sleep duration in hours (optional, float, range: 7-9)
- `Stress`: Stress level 1-5 (optional, integer, range: 1-2)
- `Hydration`: Water intake in cups/day (optional, integer, range: 8-12)
- `Smoking`: Cigarettes per day (optional, integer, range: 0)
- `Alcohol`: Drinks per day (optional, integer, range: 0-2)
- `Social Connection`: Social connection score 1-5 (optional, integer, range: 3-5)

**Response Example:**
```json
{
  "Total Score": 92,
  "Health Status": "Excellent",
  "Category Scores": {
    "Vitals": 95,
    "Lifestyle": 88,
    "Test Results": 94
  },
  "Detailed Analysis": {
    "Vitals Analysis": {
      "score": 95,
      "max_score": 100,
      "issues": [],
      "strengths": [
        "Systolic (Normal)",
        "Diastolic (Normal)",
        "Ecg (Normal)",
        "Spo2 (Normal)",
        "Temperature (Normal)",
        "Weight (Normal)",
        "Fev (Normal)",
        "Respiratory (Normal)"
      ]
    },
    "Lifestyle Analysis": {
      "score": 88,
      "max_score": 100,
      "issues": [
        "Stress (High)"
      ],
      "strengths": [
        "Exercise (Normal)",
        "Diet (Normal)",
        "Sleep (Normal)",
        "Hydration (Normal)",
        "Smoking (Normal)",
        "Alcohol (Normal)",
        "Social Connection (Normal)"
      ]
    },
    "Test Results Analysis": {
      "score": 94,
      "max_score": 100,
      "issues": [],
      "strengths": [
        "Glucose (Normal)",
        "Malaria (Negative)",
        "Widal Test (Negative)",
        "Hepatitis B (Negative)",
        "Hiv (Negative)",
        "Kidney (Normal)",
        "Lipid (Normal)",
        "Liver (Normal)"
      ]
    }
  },
  "Vitals Needing Improvement": [
    "Stress (High)"
  ],
  "Improvement Tips": [
    "🧘‍♀️ Practice stress management techniques like mindfulness meditation, deep breathing, or progressive muscle relaxation for at least 10 minutes daily."
  ],
  "Risk Factors": [],
  "Health Insights": [
    "Your overall health is excellent with a score of 92/100",
    "Your vital signs are all within healthy ranges",
    "Your test results show no concerning findings",
    "Focus on stress management to achieve optimal health"
  ]
}
```

**UI/UX Considerations:**
- Display total score prominently with color-coded health status (Green: Excellent 90-100, Blue: Good 80-89, Yellow: Fair 70-79, Orange: Poor 60-69, Red: Critical <60)
- Show category breakdown with individual progress bars for Vitals, Lifestyle, and Test Results
- Use visual indicators for strengths (✅) and areas needing improvement (⚠️)
- Display improvement tips as actionable cards with relevant icons
- Provide detailed analysis in expandable sections
- Include trend charts for historical score tracking
- Allow partial data entry with dynamic score updates

---

## Scoring Algorithm

### Category Weighting System
The Real-Time Health Score uses evidence-based weighting to prioritize different aspects of health:

**1. Vital Signs (40% weight)**
- Critical physiological indicators that reflect immediate health status
- Includes cardiovascular, respiratory, and metabolic parameters
- Real-time indicators of body function and health stability

**2. Lifestyle Factors (35% weight)**
- Preventive health determinants with long-term impact
- Modifiable behaviors that significantly influence health outcomes
- Key factors in chronic disease prevention and health optimization

**3. Test Results (25% weight)**
- Diagnostic indicators from laboratory and clinical tests
- Objective measurements of organ function and disease markers
- Important for detecting underlying health conditions

### Individual Parameter Weights

**Vital Signs Weights:**
- Systolic Blood Pressure: 12%
- Diastolic Blood Pressure: 12%
- Heart Rate (ECG): 12%
- Blood Oxygen (SpO2): 12%
- Body Weight (BMI): 12%
- Forced Expiratory Volume: 10%
- Body Temperature: 8%
- Waist Circumference: 8%
- Respiratory Rate: 8%

**Lifestyle Weights:**
- Exercise Frequency: 20%
- Diet Quality: 20%
- Sleep Duration: 15%
- Stress Level: 12%
- Smoking Status: 10%
- Hydration: 8%
- Alcohol Consumption: 8%
- Social Connection: 7%

**Test Results Weights:**
- Kidney Function (Creatinine): 18%
- Lipid Profile (Cholesterol): 18%
- Liver Function: 12%
- Blood Glucose: 12%
- Hepatitis B: 5%
- HIV Status: 5%
- Malaria: 5%
- Widal Test: 3%

### Score Calculation Process

**1. Individual Parameter Scoring:**
- Each parameter is evaluated against its normal range
- Scores are assigned based on deviation from optimal values
- "Unknown" values are excluded from calculations to avoid penalties

**2. Category Score Calculation:**
- Individual parameter scores are weighted within their category
- Category scores are normalized to 0-100 scale
- Missing data is handled gracefully without affecting other parameters

**3. Final Score Calculation:**
- Category scores are combined using evidence-based weights
- Final score is normalized and rounded to nearest integer
- Health status is assigned based on total score ranges

**4. Dynamic Adjustment:**
- Weights are automatically adjusted based on available data
- System maintains accuracy even with partial health information
- Scoring remains consistent across different data completeness levels

---

## Health Status Classifications

### Score Ranges and Interpretations

**Excellent (90-100 points)**
- 🟢 **Color Code**: Green
- **Interpretation**: Outstanding health with all major indicators in optimal ranges
- **Recommendations**: Maintain current healthy lifestyle habits
- **Monitoring**: Continue regular health assessments

**Good (80-89 points)**
- 🔵 **Color Code**: Blue  
- **Interpretation**: Good health with minor areas for improvement
- **Recommendations**: Focus on specific areas flagged for improvement
- **Monitoring**: Regular monitoring of identified areas

**Fair (70-79 points)**
- 🟡 **Color Code**: Yellow
- **Interpretation**: Moderate health concerns requiring attention
- **Recommendations**: Implement targeted lifestyle changes and consider medical consultation
- **Monitoring**: Increased monitoring frequency recommended

**Poor (60-69 points)**
- 🟠 **Color Code**: Orange
- **Interpretation**: Significant health concerns requiring intervention
- **Recommendations**: Medical consultation recommended, lifestyle modifications essential
- **Monitoring**: Close monitoring and regular health assessments needed

**Critical (<60 points)**
- 🔴 **Color Code**: Red
- **Interpretation**: Serious health concerns requiring immediate attention
- **Recommendations**: Immediate medical consultation and comprehensive health evaluation
- **Monitoring**: Urgent medical follow-up and intensive monitoring required

### Risk Factor Assessment

**Automatic Risk Alerts:**
- Blood pressure readings outside safe ranges
- Abnormal heart rate or oxygen saturation
- Positive test results for infectious diseases
- Extreme lifestyle factors (smoking, excessive alcohol)
- Critical organ function indicators

**Alert Thresholds:**
- **High Priority**: Score drops below 60 or critical vital signs
- **Medium Priority**: Score drops below 70 or concerning trends
- **Low Priority**: Score drops below 80 or minor improvements needed

---

## Improvement Recommendations System

### AI-Generated Tips Categories

**Exercise & Physical Activity:**
- Personalized activity recommendations based on current fitness level
- Progressive exercise plans for different health conditions
- Low-impact alternatives for individuals with limitations

**Nutrition & Diet:**
- Balanced diet recommendations based on health goals
- Specific nutritional guidance for identified deficiencies
- Meal planning suggestions for optimal health outcomes

**Sleep & Recovery:**
- Sleep hygiene recommendations for better rest quality
- Stress management techniques for improved sleep
- Recovery strategies for optimal health restoration

**Stress Management:**
- Mindfulness and meditation techniques
- Breathing exercises and relaxation methods
- Lifestyle modifications for stress reduction

**Preventive Care:**
- Regular health screening recommendations
- Vaccination and preventive care schedules
- Early detection strategies for health conditions

### Personalization Algorithm

**Individual Health Profile:**
- Recommendations tailored to specific health data patterns
- Consideration of existing health conditions and limitations
- Age, gender, and lifestyle-appropriate suggestions

**Priority-Based Suggestions:**
- Most impactful improvements highlighted first
- Achievable goals based on current health status
- Progressive recommendations for sustainable change

**Evidence-Based Content:**
- All recommendations based on medical research and guidelines
- Regular updates to reflect latest health science findings
- Integration with established health protocols and standards

---

## Data Integration & Compatibility

### Supported Data Formats

**Widal Test Integration:**
```json
{
  "Widal Test": {
    "Typhi O": "Reactive",
    "Typhi H": "Non-Reactive", 
    "Paratyphi AH": "Reactive",
    "Paratyphi BH": "Non-Reactive"
  }
}
```

**Legacy Format Support:**
```json
{
  "Widal Test": "Negative"
}
```

**Flexible Value Handling:**
- Numeric values: Automatic type conversion and validation
- String values: Case-insensitive processing
- Unknown values: Graceful handling without score penalties
- Missing data: Dynamic weight adjustment for accurate scoring

### Medical Device Integration

**Compatible Devices:**
- Blood pressure monitors (automatic BP readings)
- Pulse oximeters (SpO2 and heart rate data)
- Smart scales (weight and BMI calculations)
- Glucose meters (blood sugar monitoring)
- Fitness trackers (activity and sleep data)
- Smart thermometers (temperature readings)

**Data Import Standards:**
- HL7 FHIR format support for clinical data
- JSON format for device and app integrations
- CSV format for bulk data imports
- Real-time API connections for continuous monitoring

---

## Historical Tracking & Trends

### Trend Analysis Features

**Score Progression Tracking:**
- Historical score data with timestamp tracking
- Visual trend charts showing health improvements or declines
- Significant change detection with configurable thresholds

**Category-Specific Trends:**
- Individual tracking for Vitals, Lifestyle, and Test Results
- Parameter-level trend analysis for detailed insights
- Comparative analysis across different time periods

**Predictive Insights:**
- Health trajectory predictions based on current trends
- Early warning systems for declining health indicators
- Personalized goals based on historical performance

### Change Detection Thresholds

**Significant Change Indicators:**
- Overall Score: ±5% change triggers trend alert
- Vital Signs: ±10% change in individual parameters
- Lifestyle Factors: ±15% change in behavior patterns
- Test Results: ±10% change in clinical markers

**Trend Classifications:**
- **Improving**: Consistent upward trend over time
- **Stable**: Minimal variation within normal ranges
- **Declining**: Consistent downward trend requiring attention
- **Volatile**: High variation indicating need for stabilization

---

## Error Handling & Validation

### Input Validation

**Data Type Validation:**
- Automatic type conversion for numeric values
- Range validation for all health parameters
- Format validation for structured data (Widal Test)

**Common Error Responses:**
```json
{
  "error": "Invalid glucose value. Must be between 0 and 500 mg/dL",
  "field": "Glucose",
  "provided_value": -10,
  "valid_range": "0-500"
}
```

### Error Recovery Strategies

**Partial Data Processing:**
- System continues processing with available valid data
- Invalid fields are logged but don't prevent score calculation
- User receives feedback on data quality issues

**Graceful Degradation:**
- Missing categories are handled with weight redistribution
- Minimum data requirements clearly communicated
- Alternative scoring methods for incomplete datasets

---

## Performance & Optimization

### Response Times
- **Score Calculation**: < 100ms for complete health profile
- **Trend Analysis**: < 200ms for historical data processing
- **Recommendation Generation**: < 150ms for personalized tips

### Data Limits
- **Historical Data**: Unlimited storage for trend analysis
- **Concurrent Users**: Scalable architecture for multiple simultaneous assessments
- **Data Retention**: Configurable retention policies for privacy compliance

### Caching Strategy
- **Score Calculations**: Cached for identical input data
- **Recommendations**: Cached based on health profile patterns
- **Trend Analysis**: Optimized queries for historical data access

This comprehensive documentation provides everything needed for UI/UX designers and frontend developers to create an intuitive, powerful real-time health scoring system that delivers personalized health insights and actionable recommendations for optimal health management.
