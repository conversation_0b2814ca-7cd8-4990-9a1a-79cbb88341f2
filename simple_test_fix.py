#!/usr/bin/env python3
"""
Simple test to verify the health recommendation fix
"""

import requests
import json

def test_critical_health_data():
    """Test with critical health data that should trigger alerts"""
    
    print("🧪 Testing Critical Health Data...")
    
    # Submit critical health data
    health_data = {
        "user_id": "test_critical",
        "health_data": {
            "Glucose": 85.5,
            "Hepatitis B": "Negative",
            "Systolic": 120.0,
            "Diastolic": 80.0,
            "Waist Circumference": 85.0,
            "Hiv": "Negative",
            "Fev": 95.0,
            "Temperature": 37.0,
            "Ecg": 75.0,
            "Spo2": 98.0,
            "Weight": 70.5,
            "Widal Test": {
                "Typhi O": "Reactive",
                "Typhi H": "Reactive",
                "Paratyphi AH": "Reactive",
                "Paratyphi BH": "Non-Reactive"
            },
            "Malaria": "Negative",
            "Kidney": 1.0,
            "Lipid": 180.0,
            "Liver": 25.0,
            "Respiratory": 16.0,
            # Critical lifestyle factors
            "Exercise": 0,
            "Diet": 1,
            "Sleep": 4,
            "Stress": 5,
            "Hydration": 2,
            "Smoking": 10,
            "Alcohol": 5,
            "Social Connection": 1
        }
    }
    
    # Submit health data
    response = requests.post("http://127.0.0.1:8000/realtime-health-score", json=health_data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Health data submitted")
        print(f"📊 Score: {result.get('Total Score', 'Unknown')}")
        print(f"🏥 Status: {result.get('Health Status', 'Unknown')}")
        
        # Check if Widal Test results are in the data
        if "Vitals Needing Improvement" in result:
            print(f"⚠️ Issues: {result['Vitals Needing Improvement']}")
        
        return True
    else:
        print(f"❌ Failed to submit health data: {response.status_code}")
        return False

def test_simple_query():
    """Test a simple recommendation query"""
    
    print("\n💬 Testing Simple Query...")
    
    query_data = {
        "user_id": "test_critical",
        "session_id": "test_session",
        "query": "What recommendations do you have?",
        "model": "qwen2.5:1.5b"
    }
    
    try:
        response = requests.post("http://127.0.0.1:8000/query", json=query_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get("response", "")
            
            print("📝 Response received:")
            print("=" * 50)
            print(response_text[:500] + "..." if len(response_text) > 500 else response_text)
            print("=" * 50)
            
            # Check for key indicators
            checks = {
                "Has response": len(response_text) > 0,
                "Mentions Widal": "widal" in response_text.lower(),
                "Mentions typhoid": "typhoid" in response_text.lower(),
                "Has critical alert": "critical" in response_text.lower() or "urgent" in response_text.lower(),
                "Has medical attention": "medical" in response_text.lower() and "attention" in response_text.lower(),
                "Not just device rec": not (response_text.strip().endswith("turbomedics.com/products) for a great selection of health tracking devices!**") and len(response_text) < 200)
            }
            
            print("\n🔍 Response Analysis:")
            for check, passed in checks.items():
                status = "✅" if passed else "❌"
                print(f"{status} {check}")
            
            passed_count = sum(checks.values())
            total_count = len(checks)
            
            print(f"\n📊 Analysis: {passed_count}/{total_count} checks passed")
            
            if passed_count >= 3:
                print("🎉 SUCCESS: The fix appears to be working!")
                return True
            else:
                print("⚠️ PARTIAL: Some improvements detected but more work needed")
                return False
                
        else:
            print(f"❌ Query failed: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Query timed out - this might indicate the model is processing")
        return False
    except Exception as e:
        print(f"❌ Query error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Simple Health Recommendation Fix Test")
    print("=" * 50)
    
    # Test health data submission
    data_success = test_critical_health_data()
    
    if data_success:
        # Test query
        query_success = test_simple_query()
        
        print("\n" + "=" * 50)
        if query_success:
            print("🎉 OVERALL: Fix appears to be working!")
        else:
            print("⚠️ OVERALL: Partial success - needs more investigation")
    else:
        print("\n❌ OVERALL: Failed to submit test data")
