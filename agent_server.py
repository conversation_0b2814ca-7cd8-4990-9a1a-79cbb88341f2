from fastapi import Fast<PERSON><PERSON>, Request, Form, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
import uvicorn
import ollama
import faiss
import pickle
import numpy as np
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
from langchain_ollama import OllamaEmbeddings
import json
import os
import re
import sys
import traceback
import logging
from datetime import datetime, timedelta
import statistics
from dateutil import parser
import asyncio
import concurrent.futures
from functools import lru_cache, wraps
import hashlib
import time
from collections import OrderedDict
import threading
from contextlib import asynccontextmanager

# Import wellness router
from wellness_router import router as wellness_router

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('agent_server.log', encoding='utf-8')
    ]
)

# Create logger instance
logger = logging.getLogger(__name__)

# Add tools path to system path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'tools')))

# Import tools
try:
    from tools.tools_health_score import HealthScoreAnalysisTool, health_score_analysis_tool
    from tools.tools_monitor_vital_signs import monitor_vital_signs
    from tools.tools_health_data_json import get_default_health_data
    from tools.tools_kidney_function import kidney_function_analysis_tool
    from tools.tools_lipid_profile import analyze_lipid_profile
    from tools.tools_health_consult import automated_health_consultation_tool, automated_health_consultation
    from tools.tools_lung_capacity import lung_capacity_analyzer_tool
    from tools.tools_test_results import interpret_test_results
    from tools.tools_device_recommender import device_recommender_tool, device_confirmation_tool, recommend_health_devices, confirm_device_recommendation
    from tools.tools_symptom_checker import symptom_checker_tool, analyze_symptoms
    from tools.tools_chronic_tracker import chronic_tracker_tool
    from tools.tools_lab_test_explainer import explain_lab_test, list_available_tests
    from tools.tools_followup_reminder import followup_reminder_tool
    from tools.tools_chronic_risk import predict_chronic_risk
    from tools.tools_doc_summarizer import summarize_medical_text, extract_text_from_upload
    from tools.tools_lifestyle_coach import (record_habits, compute_weekly_habit_summary, generate_lifestyle_recommendations)
    from tools.tools_weekly_digest import generate_weekly_digest
    from tools.tools_progress_tracker import (generate_monthly_summary, generate_trend_recommendations)
    from tools.tools_mental_health_assessment import MentalHealthAssessmentTool
    from tools.tools_liver_function import (analyze_liver_function, extract_lft_values, MedicalConditionEnum, SmokingAlcoholEnum, DietaryHabitsEnum, MedicationsEnum, SymptomEnum, HepatitisMarkerEnum, ManualEntryRequest)

    # Import reproductive health tools
    from tools.tools_postpartum_health import postpartum_health_tool, PostpartumHealthRequest
    from tools.tools_pregnancy_monitoring import pregnancy_monitoring_tool, PregnancyMonitoringRequest
    from tools.tools_reproductive_health_tracker import reproductive_health_tracker_tool

    logger.info("Successfully imported health tools")
except ImportError as e:
    logger.error(f"Failed to import tools: {e}")
    sys.exit(1)

# JSON Cleaning Utility Functions
def clean_json_string(json_str: str) -> str:
    """Clean JSON string by removing problematic characters like non-breaking spaces"""
    try:
        # Check if cleaning is needed
        has_nbsp = '\xa0' in json_str or '\u00a0' in json_str
        has_other_issues = False

        # Replace non-breaking spaces with regular spaces
        cleaned = json_str.replace('\xa0', ' ')  # Unicode non-breaking space
        cleaned = cleaned.replace('\u00a0', ' ')  # Another form of non-breaking space

        # Remove other problematic characters
        cleaned = cleaned.replace('\u200b', '')  # Zero-width space
        cleaned = cleaned.replace('\ufeff', '')  # Byte order mark
        cleaned = cleaned.replace('\u2028', ' ')  # Line separator
        cleaned = cleaned.replace('\u2029', ' ')  # Paragraph separator

        # Handle control characters that break JSON parsing
        # Replace literal newlines, tabs, and carriage returns within string values
        import re
        # Fix unescaped control characters within JSON string values
        # This regex finds string values and properly escapes control characters within them
        def escape_control_chars(match):
            string_content = match.group(1)
            # Escape control characters
            string_content = string_content.replace('\n', '\\n')
            string_content = string_content.replace('\r', '\\r')
            string_content = string_content.replace('\t', '\\t')
            string_content = string_content.replace('\b', '\\b')
            string_content = string_content.replace('\f', '\\f')
            return f'"{string_content}"'

        # Apply control character escaping to string values
        cleaned = re.sub(r'"([^"\\]*(?:\\.[^"\\]*)*)"', escape_control_chars, cleaned)

        # Check for trailing commas (common JSON issue)
        import re
        if re.search(r',\s*[}\]]', cleaned):
            has_other_issues = True
            # Remove trailing commas before closing brackets/braces
            cleaned = re.sub(r',(\s*[}\]])', r'\1', cleaned)

        # Log if cleaning was applied
        if has_nbsp:
            nbsp_count = json_str.count('\xa0') + json_str.count('\u00a0')
            logger.info(f"JSON cleaning applied: removed {nbsp_count} non-breaking spaces")

        if has_other_issues:
            logger.info("JSON cleaning applied: removed trailing commas and other formatting issues")

        # Try to parse and re-serialize to ensure valid JSON
        try:
            json_data = json.loads(cleaned)
            return json.dumps(json_data)
        except json.JSONDecodeError as e:
            # Log the specific error for debugging
            logger.error(f"JSON still invalid after cleaning: {e}")
            logger.error(f"Error at position {e.pos}: {cleaned[max(0, e.pos-20):e.pos+20]}")
            # If parsing still fails, return the cleaned string
            return cleaned

    except Exception as e:
        logger.warning(f"Error cleaning JSON string: {e}")
        return json_str

async def parse_json_request(request: Request, required_fields: List[str] = None) -> Dict:
    """
    Parse JSON request with automatic cleaning and validation

    Args:
        request: FastAPI Request object
        required_fields: List of required field names to validate

    Returns:
        Dict: Parsed JSON data

    Raises:
        HTTPException: If JSON is invalid or required fields are missing
    """
    try:
        # Read raw request body
        body = await request.body()
        body_str = body.decode('utf-8')

        # Clean the JSON string
        cleaned_json = clean_json_string(body_str)

        try:
            # Parse the cleaned JSON
            request_data = json.loads(cleaned_json)
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON even after cleaning: {e}")
            logger.error(f"JSON error at position {e.pos}: {e.msg}")
            logger.error(f"Original body length: {len(body_str)}")
            logger.error(f"Cleaned body length: {len(cleaned_json)}")

            # Show context around the error position
            if e.pos < len(cleaned_json):
                start = max(0, e.pos - 30)
                end = min(len(cleaned_json), e.pos + 30)
                error_context = cleaned_json[start:end]
                logger.error(f"Error context: '{error_context}'")
                logger.error(f"Error position marker: {' ' * (e.pos - start)}^")

            # Check for common JSON issues
            common_issues = []
            if ',' in cleaned_json and (cleaned_json.count('{') != cleaned_json.count('}') or
                                      cleaned_json.count('[') != cleaned_json.count(']')):
                common_issues.append("Mismatched brackets/braces")
            if '"' in cleaned_json and cleaned_json.count('"') % 2 != 0:
                common_issues.append("Unmatched quotes")
            if 'null' not in cleaned_json.lower() and 'None' in cleaned_json:
                common_issues.append("Python 'None' instead of JSON 'null'")
            if 'True' in cleaned_json or 'False' in cleaned_json:
                common_issues.append("Python boolean instead of JSON boolean")

            if common_issues:
                logger.error(f"Detected common JSON issues: {', '.join(common_issues)}")

            raise HTTPException(
                status_code=422,
                detail={
                    "error": "Invalid JSON format",
                    "message": "The request contains malformed JSON. Please check for invalid characters like non-breaking spaces.",
                    "detail": [
                        {
                            "type": "json_invalid",
                            "loc": ["body", e.pos],
                            "msg": "JSON decode error",
                            "input": {},
                            "ctx": {
                                "error": e.msg,
                                "position": e.pos,
                                "context": error_context if e.pos < len(cleaned_json) else "End of input"
                            }
                        }
                    ]
                }
            )

        # Validate required fields if specified
        if required_fields:
            missing_fields = [field for field in required_fields if field not in request_data]
            if missing_fields:
                raise HTTPException(
                    status_code=422,
                    detail={
                        "error": "Missing required fields",
                        "missing_fields": missing_fields,
                        "message": f"The following required fields are missing: {', '.join(missing_fields)}"
                    }
                )

        return request_data

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error parsing JSON request: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Internal server error",
                "message": "An unexpected error occurred while processing the request."
            }
        )

# Initialize FastAPI app
app = FastAPI(title="Integrated Health Agent API",
              description="API that combines chat, health score analysis, vital signs monitoring, and health consultation")

# LINK TO WELLNESS CENTER
app.include_router(wellness_router, prefix="/api")

# Add specific exception handler for JSON decode errors
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    error_msg = f"Request validation error: {str(exc)}"
    logger.error(f"Validation error: {error_msg}")
    logger.error(f"Request path: {request.url.path}")
    logger.error(f"Request method: {request.method}")

    # Check if this is a JSON decode error
    is_json_error = any(
        error.get('type') == 'json_invalid'
        for error in exc.errors()
    )

    # Try to read the request body for debugging
    try:
        body = await request.body()
        logger.error(f"Request body: {body}")
        logger.error(f"Request body length: {len(body)}")

        # Try to decode as JSON to see where it fails
        try:
            json.loads(body)
        except json.JSONDecodeError as json_err:
            logger.error(f"JSON decode error: {json_err}")
            logger.error(f"JSON error position: {json_err.pos}")
            logger.error(f"JSON error message: {json_err.msg}")

            # Show the problematic part of the JSON
            if json_err.pos < len(body):
                start = max(0, json_err.pos - 50)
                end = min(len(body), json_err.pos + 50)
                logger.error(f"JSON context around error: {body[start:end]}")

            # Check for common issues
            body_str = body.decode('utf-8', errors='replace')
            if '\xa0' in body_str or '\u00a0' in body_str:
                logger.error("Detected non-breaking spaces in JSON - this is likely the cause of the error")

                # Try to clean and re-parse the JSON
                try:
                    cleaned_json = clean_json_string(body_str)
                    json.loads(cleaned_json)
                    logger.info("JSON cleaning would fix this issue")
                except json.JSONDecodeError:
                    logger.error("JSON cleaning did not resolve the issue")

    except Exception as body_error:
        logger.error(f"Could not read request body: {body_error}")

    # Provide more helpful error messages for JSON issues
    if is_json_error:
        return JSONResponse(
            status_code=422,
            content={
                "error": "Invalid JSON format",
                "message": "The request contains malformed JSON. Please check for invalid characters like non-breaking spaces.",
                "detail": exc.errors()
            }
        )

    return JSONResponse(
        status_code=422,
        content={"detail": exc.errors()}
    )

# Add global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    error_msg = f"Unhandled error: {str(exc)}"
    logger.error(f"Exception: {error_msg}")
    logger.error(f"Traceback: {traceback.format_exc()}")
    # Use request parameter to get the path that caused the error
    path = request.url.path if hasattr(request, 'url') else "unknown path"
    logger.error(f"Error occurred at path: {path}")
    return JSONResponse(
        status_code=500,
        content={"error": error_msg}
    )

# === MODEL CONSTANTS ===
QWEN_MODEL = "qwen2.5:1.5b"
DEEPSEEK_MODEL = "deepseek-r1:1.5b"
DEFAULT_MODEL = QWEN_MODEL

# === VECTOR STORE PATHS ===
VECTOR_STORE_PATHS = {
    QWEN_MODEL: {
        "index": r"C:\Users\<USER>\OneDrive\Desktop\MCP\Vector_Store\qwen2.5-1.5b\index.faiss",
        "metadata": r"C:\Users\<USER>\OneDrive\Desktop\MCP\Vector_Store\qwen2.5-1.5b\index.pkl"
    },
    DEEPSEEK_MODEL: {
        "index": r"C:\Users\<USER>\OneDrive\Desktop\MCP\Vector_Store\deepseek-r1-1.5b\index.faiss",
        "metadata": r"C:\Users\<USER>\OneDrive\Desktop\MCP\Vector_Store\deepseek-r1-1.5b\index.pkl"
    }
}

# === STORE CHAT TITLES, HISTORIES, AND USER HEALTH DATA ===
chat_titles: Dict[tuple, str] = {}  # Dictionary to store session titles per user (patient_id, session_id)
chat_histories: Dict[tuple, List[Dict[str, str]]] = {}  # Dictionary to store chat histories by (patient_id, session_id)
user_health_data: Dict[tuple, Dict[str, Any]] = {}  # Dictionary to store health data by (patient_id, session_id)
MAX_HISTORY_LENGTH = 10
# TOP_K = 1  # Number of relevant documents to fetch

# === LOAD FAISS INDEXES & METADATA ===
vector_indexes = {}
vector_docs = {}
embedding_models = {}

for model_name, paths in VECTOR_STORE_PATHS.items():
    try:
        vector_indexes[model_name] = faiss.read_index(paths["index"])
        logger.info(f"✅ FAISS index loaded for {model_name}")
    except Exception as e:
        logger.error(f"❌ Error loading FAISS index for {model_name}: {e}")

    try:
        with open(paths["metadata"], "rb") as f:
            vector_docs[model_name] = pickle.load(f)
        logger.info(f"✅ Metadata loaded for {model_name}")
    except Exception as e:
        logger.error(f"❌ Error loading metadata for {model_name}: {e}")

    try:
        embedding_models[model_name] = OllamaEmbeddings(model=model_name)
        logger.info(f"✅ Embedding model loaded for {model_name}")
    except Exception as e:
        logger.error(f"❌ Error loading embedding model {model_name}: {e}")

# === REQUEST MODELS ===
class ChatRequest(BaseModel):
    session_id: str
    user_id: str
    query: str
    model: str = DEFAULT_MODEL

class VitalSignsRequest(BaseModel):
    user_id: str
    vital_signs: Dict[str, Any]

class HealthScoreRequest(BaseModel):
    user_id: str
    health_data: Dict[str, Any]  # Allow any type of value (string, float, etc.)

class KidneyFunctionRequest(BaseModel):
    user_id: str
    kidney_data: Dict[str, Any]  # Accepts both float and string types like "Sex"

class LipidProfileRequest(BaseModel):
    user_id: str
    lipid_data: Dict[str, Any]  # Accepts both string and numeric values

class LungCapacityRequest(BaseModel):
    user_id: str
    spirometry_data: Dict[str, Any]  # Accepts both numeric values and patient information

class UserIDRequest(BaseModel):
    user_id: str

class TestResultsRequest(BaseModel):
    user_id: str
    test_results: Dict[str, Any]  # Contains test results and patient information

class RealTimeHealthScoreRequest(BaseModel):
    user_id: str
    health_data: Dict[str, Any]  # Contains vitals, lifestyle factors, and test results

class DeviceRecommendationRequest(BaseModel):
    user_id: str
    health_data: Optional[Dict[str, Any]] = None  # Optional health data, will use stored data if not provided
    test_type: Optional[str] = None  # Type of test (vital_signs, kidney_function, lipid_profile, etc.)
    test_data: Optional[Dict[str, Any]] = None  # Test-specific data

class DeviceConfirmationRequest(BaseModel):
    user_id: str
    confirmed: bool  # Whether user confirmed they want device recommendations

class SymptomCheckerRequest(BaseModel):
    user_id: str
    symptoms: str
    age: Optional[int] = None
    sex: Optional[str] = None
    duration: Optional[str] = None
    severity: Optional[str] = "moderate"

class ChronicTrackerRequest(BaseModel):
    user_id: str
    condition_data: Dict[str, Any]  # Contains chronic condition data and tracking information
    tracking_frequency: Optional[str] = "as_needed"  # Frequency of tracking: "daily", "weekly", "monthly", "as_needed"
    measurement_date: Optional[str] = None  # Date/time of measurement (ISO format)

class LabTestExplainerRequest(BaseModel):
    user_id: str
    test_name: Optional[str] = None
    list_tests: bool = False

class FollowupReminderRequest(BaseModel):
    user_id: str
    health_data: Optional[Dict[str, Any]] = None  # Optional health data, will use stored data if not provided
    test_type: Optional[str] = None  # Type of test (vital_signs, kidney_function, lipid_profile, etc.)

class ProgressTrackRequest(BaseModel):
    user_id: str
    vital_signs: Dict[str, Any]

class ChronicRiskRequest(BaseModel):
    user_id: str
    chronic_data: Dict[str, Any]

class UserProfileRequest(BaseModel):
    user_id: str
    profile: Dict[str, Any]

class DocSummaryRequest(BaseModel):
    user_id: str
    model: Optional[str] = DEFAULT_MODEL  # Optional fallback

class LifestyleHabitRequest(BaseModel):
    user_id: str
    habits: Dict[str, float]  # e.g., {"walk": 5000, "water": 6, ...}

class DigestRequest(BaseModel):
    user_id: str
    vital_signs: Dict[str, Any]

class MentalHealthAssessmentRequest(BaseModel):
    user_id: str
    assessment_data: Dict[str, Any]  # Contains age, gender, country, stress responses, PHQ-9, GAD-7, etc.


class LiverFunctionAssessmentRequest(BaseModel):
    user_id: str
    lft_data: ManualEntryRequest

# Removed LabInsightsDashboardRequest - functionality moved to unified lab manager

# Unified Reproductive Health Request Model
class ReproductiveHealthRequest(BaseModel):
    user_id: str
    mode: str  # "cycle" or "lifestyle"
    # For cycle mode
    start_date: Optional[str] = None
    period_duration: Optional[int] = None
    # For lifestyle mode
    activities: Optional[List[Dict[str, Any]]] = None

# === HELPER FUNCTIONS ===
def generate_chat_title(first_query: str) -> str:
    """Generate a title by extracting key words from the query."""
    words = first_query.split()[:10]  # Take the first 5 words
    return " ".join(words).title()

# === ADVANCED CACHING AND PARALLEL PROCESSING SYSTEM ===

class SmartCache:
    """Advanced multi-level caching system similar to ChatGPT"""

    def __init__(self, max_size=10000, ttl_seconds=3600):
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.cache = OrderedDict()
        self.access_times = {}
        self.hit_counts = {}
        self.lock = threading.RLock()

    def _generate_key(self, *args, **kwargs):
        """Generate cache key from arguments with improved normalization"""
        # Normalize args - remove user-specific data that shouldn't affect caching
        normalized_args = []
        for arg in args:
            if isinstance(arg, str):
                # Normalize query strings by removing extra whitespace and lowercasing
                if len(arg) > 10:  # Likely a query string
                    normalized_args.append(arg.strip().lower())
                else:
                    normalized_args.append(arg)
            else:
                normalized_args.append(arg)

        # Normalize kwargs - exclude user-specific keys
        normalized_kwargs = {}
        exclude_keys = {'user_id', 'session_id', 'timestamp', 'patient_id'}
        for k, v in kwargs.items():
            if k not in exclude_keys:
                if isinstance(v, str) and len(v) > 10:
                    normalized_kwargs[k] = v.strip().lower()
                else:
                    normalized_kwargs[k] = v

        key_data = str(tuple(normalized_args)) + str(sorted(normalized_kwargs.items()))
        return hashlib.md5(key_data.encode()).hexdigest()

    def _is_expired(self, key):
        """Check if cache entry is expired"""
        if key not in self.access_times:
            return True
        return time.time() - self.access_times[key] > self.ttl_seconds

    def _evict_lru(self):
        """Evict least recently used items"""
        while len(self.cache) >= self.max_size:
            lru_key = next(iter(self.cache))
            del self.cache[lru_key]
            del self.access_times[lru_key]
            del self.hit_counts[lru_key]

    def get(self, key):
        """Get item from cache"""
        with self.lock:
            if key in self.cache and not self._is_expired(key):
                # Move to end (most recently used)
                self.cache.move_to_end(key)
                self.access_times[key] = time.time()
                self.hit_counts[key] = self.hit_counts.get(key, 0) + 1
                return self.cache[key]
            elif key in self.cache:
                # Remove expired entry
                del self.cache[key]
                del self.access_times[key]
                del self.hit_counts[key]
            return None

    def set(self, key, value):
        """Set item in cache"""
        with self.lock:
            self._evict_lru()
            self.cache[key] = value
            self.access_times[key] = time.time()
            self.hit_counts[key] = 0

    def get_stats(self):
        """Get cache statistics"""
        with self.lock:
            total_hits = sum(self.hit_counts.values())
            return {
                "size": len(self.cache),
                "max_size": self.max_size,
                "total_hits": total_hits,
                "hit_rate": total_hits / max(1, len(self.cache))
            }

class ParallelProcessor:
    """Parallel processing manager for concurrent operations"""

    def __init__(self, max_workers=4):
        self.max_workers = max_workers
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)

    async def run_parallel(self, tasks):
        """Run multiple tasks in parallel"""
        loop = asyncio.get_event_loop()
        futures = []

        for task_func, args, kwargs in tasks:
            future = loop.run_in_executor(
                self.executor,
                lambda: task_func(*args, **kwargs)
            )
            futures.append(future)

        results = await asyncio.gather(*futures, return_exceptions=True)
        return results

    async def run_with_timeout(self, task_func, timeout=30, *args, **kwargs):
        """Run task with timeout"""
        try:
            loop = asyncio.get_event_loop()
            future = loop.run_in_executor(
                self.executor,
                lambda: task_func(*args, **kwargs)
            )
            return await asyncio.wait_for(future, timeout=timeout)
        except asyncio.TimeoutError:
            logger.warning(f"Task {task_func.__name__} timed out after {timeout}s")
            return None

# Initialize advanced caching and parallel processing
smart_cache = SmartCache(max_size=10000, ttl_seconds=3600)  # 1 hour TTL
parallel_processor = ParallelProcessor(max_workers=6)

# Specialized caches for different data types with optimized TTL
embedding_cache_smart = SmartCache(max_size=5000, ttl_seconds=86400)  # 24 hours for embeddings (stable)
health_analysis_cache = SmartCache(max_size=2000, ttl_seconds=7200)  # 2 hours for health analysis
consultation_cache = SmartCache(max_size=1000, ttl_seconds=3600)  # 1 hour for consultations

# Legacy simple cache (keeping for backward compatibility)
embedding_cache = {}

# Simple query response cache for common health queries
query_response_cache = SmartCache(max_size=1000, ttl_seconds=3600)  # 1 hour for query responses

# Common health query patterns that can be cached
CACHEABLE_QUERY_PATTERNS = [
    "recommendation", "advice", "suggest", "tips", "what should i do",
    "health score", "analyze", "assessment", "status", "check",
    "improve", "better", "help", "guidance", "plan"
]

def is_query_cacheable(query: str) -> bool:
    """Check if a query can be cached based on common patterns"""
    query_lower = query.lower()
    return any(pattern in query_lower for pattern in CACHEABLE_QUERY_PATTERNS)

def clean_llm_response_duplicates(llm_response: str) -> str:
    """Remove duplicated critical alert content from LLM response while keeping valuable insights"""
    if not llm_response:
        return llm_response

    # Patterns to remove from LLM response to avoid duplication
    duplicate_patterns = [
        r"🚨.*?CRITICAL.*?🚨.*?\n",
        r"🚨.*?URGENT MEDICAL ATTENTION.*?\n",
        r".*?Widal Test shows.*?reactive antibodies.*?\n",
        r".*?typhoid infection.*?immediate medical consultation.*?\n",
        r".*?Positive results detected.*?\n",
        r".*?IMMEDIATE ACTIONS REQUIRED.*?\n",
        r".*?Contact your healthcare provider immediately.*?\n",
        r".*?Do not self-medicate.*?\n",
        r".*?lifestyle score is 0%.*?\n"
    ]

    import re
    cleaned_response = llm_response

    # Remove duplicate patterns but keep the rest of the LLM response
    for pattern in duplicate_patterns:
        cleaned_response = re.sub(pattern, "", cleaned_response, flags=re.IGNORECASE | re.MULTILINE)

    # Clean up extra newlines and whitespace
    cleaned_response = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_response)
    cleaned_response = cleaned_response.strip()

    # If the response is too short after cleaning, return a minimal version
    if len(cleaned_response) < 50:
        return "Based on your health data analysis, please follow the critical recommendations above."

    return cleaned_response

def sync_cached(cache_instance=None, ttl=3600):
    """Decorator for sync function caching"""
    def decorator(func):
        cache = cache_instance or smart_cache

        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = cache._generate_key(func.__name__, *args, **kwargs)

            # Try to get from cache
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                logger.info(f"Cache hit for {func.__name__}")
                return cached_result

            # Execute function
            logger.info(f"Cache miss for {func.__name__} - executing")
            result = func(*args, **kwargs)

            # Cache result
            cache.set(cache_key, result)
            return result

        return wrapper
    return decorator

def async_cached(cache_instance=None, ttl=3600):
    """Decorator for async function caching"""
    def decorator(func):
        cache = cache_instance or smart_cache

        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = cache._generate_key(func.__name__, *args, **kwargs)

            # Try to get from cache
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                logger.info(f"Cache hit for {func.__name__}")
                return cached_result

            # Execute function
            logger.info(f"Cache miss for {func.__name__} - executing")
            result = await func(*args, **kwargs)

            # Cache result
            cache.set(cache_key, result)
            return result

        return wrapper
    return decorator

@sync_cached(cache_instance=embedding_cache_smart, ttl=86400)
def retrieve_context_cache(query: str, model_name: str):
    """Retrieve relevant context from the vector store with smart caching"""
    if model_name not in vector_indexes or model_name not in vector_docs:
        logger.warning(f"Vector index or docs not found for model {model_name}")
        return ""

    try:
        # Normalize query for better caching
        normalized_query = query.strip().lower()

        # Get the embedding model
        embedder = embedding_models[model_name]

        # Generate embedding for the normalized query
        query_embedding = np.array([embedder.embed_query(normalized_query)]).astype("float32")

        # Search the vector index
        index = vector_indexes[model_name]
        documents = vector_docs[model_name]

        _, indices = index.search(query_embedding, k=3)  # Reduced to top 3 for faster processing

        # Get the relevant documents
        relevant_docs = [
            documents.get(int(idx), {}).get("text", "") if isinstance(documents.get(int(idx), {}), dict)
            else str(documents.get(int(idx), ""))
            for idx in indices[0]
        ]

        result = " ".join(relevant_docs)
        logger.info(f"Vector store retrieved {len(relevant_docs)} documents, total length: {len(result)} chars")
        return result
    except Exception as e:
        logger.error(f"Error retrieving context: {e}")
        return ""

def convert_user_id_to_key(user_id: str) -> tuple:
    """Convert user_id string to tuple format (patient_id, session_id)"""
    if "_" in user_id:
        parts = user_id.split("_", 1)
        return (parts[0], parts[1])
    else:
        # For backward compatibility, use user_id as patient_id and "default" as session_id
        return (user_id, "default")

def add_health_record(user_key: tuple, vitals: Dict[str, float], user_health_data: Dict):
    entry = {
        "timestamp": datetime.now().isoformat()
    }
    entry.update(vitals)
    user_health_data.setdefault(user_key, []).append(entry)


@sync_cached(cache_instance=health_analysis_cache, ttl=1800)
def analyze_health_score(health_data: Dict[str, Any]) -> Dict:
    """Analyze health score data"""
    try:
        # Log the input data for debugging
        logger.info(f"Health data received: {json.dumps(health_data)}")

        # Process the health data to ensure it's in the correct format
        processed_data = {}
        for key, value in health_data.items():
            # Handle Widal Test with detailed antibody results
            if key == "Widal Test":
                if isinstance(value, dict):
                    # Handle detailed Widal Test format
                    processed_data[key] = value
                elif value == "Unknown" or value is None or value == "" or value == "null":
                    processed_data[key] = "Unknown"
                else:
                    # Handle legacy string format
                    processed_data[key] = value
            # Handle other test results with string values
            elif key in ["Malaria", "Hepatitis B", "Hiv"]:
                # Ensure "Unknown" is preserved as "Unknown"
                if value == "Unknown" or value is None or value == "" or value == "null":
                    processed_data[key] = "Unknown"
                else:
                    processed_data[key] = value
            # Handle "Unknown" values for all other fields
            elif value == "Unknown":
                # Skip Unknown values completely - don't add them to processed_data
                # This ensures they won't be evaluated at all
                continue
            # Handle None values
            elif value is None or value == "" or value == "null":
                # Skip null values completely - don't add them to processed_data
                # This ensures they won't be evaluated at all
                continue
            # Handle integer values (Systolic, Diastolic)
            elif key in ["Systolic", "Diastolic"]:
                try:
                    processed_data[key] = int(value)
                except (ValueError, TypeError):
                    # If conversion fails, keep the original value
                    processed_data[key] = value
            # Convert other numeric values to float
            else:
                try:
                    processed_data[key] = float(value)
                except (ValueError, TypeError):
                    # If conversion fails, keep the original value
                    processed_data[key] = value

        logger.info(f"Processed health data: {json.dumps(processed_data)}")

        # Initialize the custom health score tool directly
        logger.info("Initializing CustomHealthScoreAnalysisTool")

        # Create a custom subclass to override the generate_report method
        class CustomHealthScoreAnalysisTool(HealthScoreAnalysisTool):
            def generate_report(self, health_data: dict) -> dict:
                total_score = 0
                max_score = 0
                vitals_needing_improvement = []
                improvement_tips = []

                # Track categories to avoid repetitive tips
                tip_categories = {
                    "doctor_visit": False,  # For serious issues requiring medical attention
                    "breathing": False,     # For respiratory-related tips
                    "diet": False,          # For nutrition-related tips
                    "exercise": False,      # For physical activity tips
                    "blood_pressure": False # For blood pressure related tips
                }

                # Track abnormal test results to consolidate recommendations
                abnormal_tests = []

                # We've already filtered out null values in the analyze_health_score function
                # This loop will only process non-null values
                for key, value in health_data.items():

                    # Handle test results properly
                    if key == "Widal Test":
                        max_score += 5
                        if isinstance(value, dict):
                            # Handle detailed Widal Test format
                            reactive_count = 0
                            total_antibodies = 0
                            reactive_antibodies = []

                            for antibody, result in value.items():
                                if result not in ["Unknown", None, "", "null"]:
                                    total_antibodies += 1
                                    if result.lower() == "reactive":
                                        reactive_count += 1
                                        reactive_antibodies.append(antibody)

                            if total_antibodies == 0:
                                # All unknown, don't penalize
                                max_score -= 5
                            elif reactive_count == 0:
                                # All non-reactive (healthy)
                                total_score += 5
                            else:
                                # Some reactive antibodies (potential infection)
                                vitals_needing_improvement.append(f"Widal Test (Reactive: {', '.join(reactive_antibodies)})")
                                abnormal_tests.append("Widal Test")
                        elif isinstance(value, str):
                            # Handle legacy string format
                            if value.lower() == "negative":
                                total_score += 5
                            elif value.lower() == "unknown":
                                max_score -= 5
                            else:
                                vitals_needing_improvement.append(f"Widal Test (Positive)")
                                abnormal_tests.append("Widal Test")
                    elif key in ["Malaria", "Hepatitis B", "Hiv"]:
                        max_score += 5
                        if isinstance(value, str):
                            if value.lower() == "negative":
                                total_score += 5
                            elif value.lower() == "unknown":
                                # Don't count Unknown as needing improvement
                                # Also don't add to max_score to avoid penalizing for unknown values
                                max_score -= 5
                            else:
                                vitals_needing_improvement.append(f"{key} (Positive)")
                                abnormal_tests.append(key)
                    # Handle other metrics using the parent class logic
                    elif key == "Weight":
                        # This field stores the BMI value, not the weight
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if value < 18.5:
                                vitals_needing_improvement.append(f"{key} (Low)")
                                if not tip_categories["diet"]:
                                    improvement_tips.append("🥗 Consider adding more nutrient-dense foods like nuts, avocados, and protein-rich meals to help you reach a healthier weight.")
                                    tip_categories["diet"] = True
                            elif 18.5 <= value <= 24.9:
                                total_score += 10
                            elif 25 <= value <= 29.9:
                                total_score += 5
                                vitals_needing_improvement.append(f"{key} (Moderately High)")
                                if not tip_categories["exercise"]:
                                    improvement_tips.append("🚶‍♀️ Try incorporating 30 minutes of moderate activity most days and being mindful of portion sizes to help manage your weight.")
                                    tip_categories["exercise"] = True
                            else:
                                vitals_needing_improvement.append(f"{key} (High)")
                                if not tip_categories["diet"] and not tip_categories["exercise"]:
                                    improvement_tips.append("💪 Consider consulting with a nutritionist about a personalized meal plan and finding enjoyable physical activities that fit your lifestyle.")
                                    tip_categories["diet"] = True
                                    tip_categories["exercise"] = True
                    elif key == "Glucose":
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if 70 <= value <= 100:
                                total_score += 10
                            elif 100 < value <= 125:
                                total_score += 5
                                vitals_needing_improvement.append(f"{key} (Moderately High)")
                                if not tip_categories["diet"]:
                                    improvement_tips.append("🍎 To help manage your slightly elevated glucose, focus on complex carbohydrates, limit added sugars, and pair carbs with proteins to slow absorption.")
                                    tip_categories["diet"] = True
                            else:
                                vitals_needing_improvement.append(f"{key} (Abnormal)")
                                if not tip_categories["doctor_visit"]:
                                    improvement_tips.append("🩺 Your glucose levels warrant a discussion with your healthcare provider who can recommend appropriate monitoring and management strategies.")
                                    tip_categories["doctor_visit"] = True
                    elif key == "Spo2":
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if value >= 95:
                                total_score += 10
                            else:
                                vitals_needing_improvement.append(f"{key} (Low)")
                                if not tip_categories["breathing"]:
                                    improvement_tips.append("🫁 Practice diaphragmatic breathing exercises (belly breathing) for 5-10 minutes several times daily to help improve your oxygen saturation levels.")
                                    tip_categories["breathing"] = True
                    elif key == "Temperature":
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if 36.5 <= value <= 37.5:
                                total_score += 10
                            else:
                                vitals_needing_improvement.append(f"{key} (Abnormal)")
                                if value > 37.5:
                                    improvement_tips.append("🌡️ For your elevated temperature, ensure you're well-hydrated, rest adequately, and monitor for any changes. If it persists beyond 48 hours, consult your doctor.")
                                else:
                                    improvement_tips.append("🌡️ Your temperature is running a bit low. Keep warm, stay hydrated, and monitor for any other symptoms that might accompany this reading.")
                    elif key == "Ecg":
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if 60 <= value <= 100:
                                total_score += 10
                            else:
                                vitals_needing_improvement.append(f"{key} (Abnormal)")
                                if value > 100:
                                    improvement_tips.append("❤️ For your elevated heart rate, try practicing mindfulness meditation, limiting caffeine, and ensuring adequate rest. If it remains consistently high, consult your doctor.")
                                else:
                                    improvement_tips.append("❤️ Your heart rate is on the lower side. This can be normal for physically fit individuals, but monitor for any symptoms like dizziness or fatigue.")
                    elif key == "Systolic" or key == "Diastolic":
                        max_score += 5
                        is_systolic = key == "Systolic"
                        normal_range = (90, 120) if is_systolic else (60, 80)

                        if isinstance(value, (int, float)):
                            if normal_range[0] <= value <= normal_range[1]:
                                total_score += 5
                            else:
                                vitals_needing_improvement.append(f"{key} (Abnormal)")

                                # Only add blood pressure tip if we haven't already
                                if not tip_categories["blood_pressure"]:
                                    if (is_systolic and value > normal_range[1]) or (not is_systolic and value > normal_range[1]):
                                        improvement_tips.append("🩸 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.")
                                    else:
                                        improvement_tips.append("🩸 Your blood pressure reading is on the lower side. Stay well-hydrated, rise slowly from sitting/lying positions, and discuss with your doctor if you experience dizziness or fatigue.")

                                    tip_categories["blood_pressure"] = True
                    elif key == "Fev":
                        # Only evaluate if value is not None
                        if value is not None:
                            max_score += 5
                            if isinstance(value, (int, float)) and value >= 80:
                                total_score += 5
                            else:
                                vitals_needing_improvement.append(f"{key} (Low)")
                                if not tip_categories["breathing"]:
                                    improvement_tips.append("🫁 To improve your lung function, practice pursed-lip breathing (inhale through nose for 2 counts, exhale through pursed lips for 4 counts) and consider using an incentive spirometer if recommended by your doctor.")
                                    tip_categories["breathing"] = True
                    elif key == "Waist Circumference":
                        max_score += 10
                        if isinstance(value, (int, float)):
                            # Gender-specific waist circumference thresholds
                            # Using general thresholds since gender may not be available
                            if value <= 88:  # Generally healthy for women, very good for men
                                total_score += 10
                            elif value <= 102:  # Borderline for men, elevated for women
                                total_score += 5
                                vitals_needing_improvement.append(f"{key} (Elevated)")
                                if not tip_categories["exercise"]:
                                    improvement_tips.append("🏃‍♀️ Focus on core-strengthening exercises and cardiovascular activities to help reduce waist circumference and improve overall health.")
                                    tip_categories["exercise"] = True
                            else:
                                vitals_needing_improvement.append(f"{key} (High)")
                                if not tip_categories["diet"] and not tip_categories["exercise"]:
                                    improvement_tips.append("💪 Consider a comprehensive approach with both dietary changes and regular physical activity to help reduce waist circumference and lower health risks.")
                                    tip_categories["diet"] = True
                                    tip_categories["exercise"] = True
                    elif key == "Kidney":
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if value >= 90:  # Normal kidney function (eGFR)
                                total_score += 10
                            elif value >= 60:  # Mild decrease
                                total_score += 7
                                vitals_needing_improvement.append(f"{key} (Mildly Decreased)")
                                improvement_tips.append("💧 Stay well-hydrated, limit sodium intake, and monitor blood pressure to support kidney health.")
                            elif value >= 30:  # Moderate decrease
                                total_score += 3
                                vitals_needing_improvement.append(f"{key} (Moderately Decreased)")
                                if not tip_categories["doctor_visit"]:
                                    improvement_tips.append("🩺 Your kidney function shows moderate decline. Please consult with a nephrologist for proper evaluation and management.")
                                    tip_categories["doctor_visit"] = True
                            else:  # Severe decrease
                                vitals_needing_improvement.append(f"{key} (Severely Decreased)")
                                if not tip_categories["doctor_visit"]:
                                    improvement_tips.append("🚨 Your kidney function requires immediate medical attention. Please consult with a nephrologist as soon as possible.")
                                    tip_categories["doctor_visit"] = True
                    elif key == "Lipid":
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if value < 200:  # Total cholesterol < 200 mg/dL
                                total_score += 10
                            elif value < 240:  # Borderline high
                                total_score += 5
                                vitals_needing_improvement.append(f"{key} (Borderline High)")
                                if not tip_categories["diet"]:
                                    improvement_tips.append("🥗 Focus on heart-healthy foods like oats, fish, nuts, and olive oil while limiting saturated fats to help manage cholesterol levels.")
                                    tip_categories["diet"] = True
                            else:  # High
                                vitals_needing_improvement.append(f"{key} (High)")
                                if not tip_categories["doctor_visit"]:
                                    improvement_tips.append("🩺 Your cholesterol levels are elevated. Consider consulting with your healthcare provider about management strategies.")
                                    tip_categories["doctor_visit"] = True
                    elif key == "Liver":
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if value <= 40:  # Normal ALT levels (example threshold)
                                total_score += 10
                            elif value <= 80:  # Mildly elevated
                                total_score += 5
                                vitals_needing_improvement.append(f"{key} (Mildly Elevated)")
                                improvement_tips.append("🍃 Support liver health by limiting alcohol, maintaining a healthy weight, and eating antioxidant-rich foods.")
                            else:  # Significantly elevated
                                vitals_needing_improvement.append(f"{key} (Elevated)")
                                if not tip_categories["doctor_visit"]:
                                    improvement_tips.append("🩺 Your liver function tests show elevation. Please consult with your healthcare provider for proper evaluation.")
                                    tip_categories["doctor_visit"] = True
                    elif key == "Respiratory":
                        max_score += 10
                        if isinstance(value, (int, float)):
                            if value >= 80:  # Good respiratory function
                                total_score += 10
                            elif value >= 60:  # Mild impairment
                                total_score += 5
                                vitals_needing_improvement.append(f"{key} (Mild Impairment)")
                                if not tip_categories["breathing"]:
                                    improvement_tips.append("🫁 Practice deep breathing exercises and consider light aerobic activities to help improve respiratory function.")
                                    tip_categories["breathing"] = True
                            else:  # Significant impairment
                                vitals_needing_improvement.append(f"{key} (Impaired)")
                                if not tip_categories["doctor_visit"]:
                                    improvement_tips.append("🩺 Your respiratory function shows significant impairment. Please consult with a pulmonologist for proper evaluation.")
                                    tip_categories["doctor_visit"] = True

                # Add consolidated recommendation for abnormal test results
                if abnormal_tests:
                    improvement_tips.append(f"🩺 Your test results for {', '.join(abnormal_tests)} require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps.")

                # Normalize score
                final_score = round((total_score / max_score) * 100) if max_score > 0 else 0

                # Health status logic
                if final_score >= 85:
                    status = "Excellent"
                elif final_score >= 70:
                    status = "Good"
                elif final_score >= 50:
                    status = "Fair"
                else:
                    status = "Poor"

                # If no improvement tips were generated but there are vitals needing improvement
                if not improvement_tips and vitals_needing_improvement:
                    improvement_tips.append("🌟 Some of your health metrics could use attention. Focus on a balanced diet, regular exercise, and adequate rest to improve your overall health.")

                # If everything is normal
                if not improvement_tips and not vitals_needing_improvement:
                    improvement_tips.append("🌟 Fantastic job maintaining your health! Keep up your great habits with regular exercise, balanced nutrition, and good sleep. You're doing wonderfully!")

                return {
                    "Total Score": final_score,
                    "Health Status": status,
                    "Vitals Needing Improvement": vitals_needing_improvement if vitals_needing_improvement else ["None"],
                    "Improvement Tips": improvement_tips
                }

        # Use the custom tool to generate the report
        custom_tool = CustomHealthScoreAnalysisTool()
        result = custom_tool.generate_report(processed_data)
        logger.info(f"Health score report generated: {json.dumps(result)}")

        return result
    except Exception as e:
        error_msg = f"Failed to analyze health score: {str(e)}"
        logger.error(error_msg)
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise Exception(error_msg)

def process_vital_signs(vital_signs: Dict[str, float]) -> Dict:
    """Process vital signs data and return analysis"""
    try:
        # Format data for the tool
        vital_signs_json = json.dumps({"data": vital_signs})
        logging.info(f"Processing vital signs: {vital_signs_json}")

        # Use the vital sign monitoring tool
        result = monitor_vital_signs(vital_signs_json)
        logging.info(f"Vital signs monitoring result: {result}")

        # Check for abnormal patterns
        alerts = []
        severity = "Normal"
        if vital_signs.get("Glucose") and vital_signs["Glucose"] > 140:
            alerts.append("⚠️ High glucose levels detected, Possible hyperglycemia. Consider consulting a doctor.")
            severity = "Critical"
        if vital_signs.get("Spo2") and vital_signs["Spo2"] < 95:
            alerts.append("⚠️ Low Spo2 levels detected, Possible hypoxemia. Ensure proper ventilation.")
            severity = "Critical"
        if vital_signs.get("Ecg") and vital_signs["Ecg"] > 100:
            alerts.append("⚠️ High heart rate detected. Practice stress management.")
            severity = "Caution"
        if vital_signs.get("Temperature") and vital_signs["Temperature"] > 37.5:
            alerts.append("⚠️ Fever detected. Stay hydrated and consult a doctor if it persists.")
            severity = "Caution"

        alert_text = "\n".join(alerts) if alerts else "✅ No abnormal patterns detected."

        return {
            "analysis": result,
            "alerts": alert_text,
            "suggest_consultation": len(alerts) > 0,
            "recommendation": "Please consult your doctor." if severity == "Critical" else "Continue monitoring regularly."
        }
    except Exception as e:
        error_msg = f"Failed to process vital signs: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        raise Exception(error_msg)

def get_previous_intent(user_key) -> str:
    """Extract the most recent intent from chat history"""
    if user_key not in chat_histories or len(chat_histories[user_key]) < 3:
        return ""

    # Look at the last assistant message
    for message in reversed(chat_histories[user_key]):
        if message.get("role") == "assistant":
            content = message.get("content", "").lower()

            # Check for intent patterns in the message
            if "would you like to analyze your health score" in content:
                return "health_score"
            elif "would you like to generate a real-time health score" in content or "would you like to see your real-time health score" in content:
                return "realtime_health_score"
            elif "would you like to enter your vital signs" in content:
                return "vital_signs"
            elif "would you like to analyze your kidney function" in content:
                return "kidney_function"
            elif "would you like to analyze your lipid profile" in content:
                return "lipid_profile"
            elif "would you like to analyze your lung capacity" in content or "would you like to check your respiratory health" in content:
                return "lung_capacity"
            elif "would you like to start a comprehensive health consultation" in content or "would you like to start a health consultation" in content or "would you like me to do that now" in content:
                return "health_consultation"
            elif "would you like me to recommend health devices" in content or "would you like to see device recommendations" in content:
                return "device_recommendations"
            elif any(
                phrase in content
                for phrase in [
                    "would you like to track diabetes",
                    "would you like to track hypertension",
                    "would you like to track asthma",
                    "would you like to track heart disease",
                    "would you like to track kidney disease",
                    "would you like to track your chronic condition",
                ]
            ):
                return "chronic_tracker"

    return ""

def analyze_health_trends(user_key) -> str:
    """Analyze trends in user's health data over time"""
    if user_key not in user_health_data:
        return ""

    user_data = user_health_data[user_key]
    trend_analysis = "📈 **Health Trends Analysis**\n\n"
    has_trends = False

    # Check if we have multiple health score entries
    if "health_score" in user_data:
        # For now, we're just showing the most recent score
        # In a real implementation, you would store historical data and show trends
        score_data = user_data["health_score"]
        trend_analysis += f"**Health Score**: {score_data['result'].get('Total Score', 0)} ({score_data['result'].get('Health Status', 'Unknown')})\n"
        trend_analysis += f"Last measured: {score_data['timestamp'][:10]}\n\n"
        has_trends = True

    # Check vital signs trends
    if "vital_signs" in user_data:
        vital_data = user_data["vital_signs"]
        trend_analysis += "**Vital Signs**:\n"
        for key, value in vital_data['data'].items():
            trend_analysis += f"- {key}: {value}\n"
        trend_analysis += f"Last measured: {vital_data['timestamp'][:10]}\n\n"
        has_trends = True

    # Add kidney function if available
    if "kidney_function" in user_data:
        kidney_data = user_data["kidney_function"]
        trend_analysis += f"**Kidney Function**: {kidney_data['result'].get('overall_health', 'Unknown')}\n"
        trend_analysis += f"Last measured: {kidney_data['timestamp'][:10]}\n\n"
        has_trends = True

    # Add lipid profile if available
    if "lipid_profile" in user_data:
        lipid_data = user_data["lipid_profile"]
        trend_analysis += f"**Lipid Profile**: ASCVD Risk - {lipid_data['result'].get('ascvd_risk', 'Unknown')}\n"
        trend_analysis += f"Last measured: {lipid_data['timestamp'][:10]}\n\n"
        has_trends = True

    # Add lung capacity if available
    if "lung_capacity" in user_data:
        lung_data = user_data["lung_capacity"]
        trend_analysis += f"**Respiratory Health**: Risk Level - {lung_data['result'].get('respiratory_risk_level', 'Unknown')}\n"

        # Add potential conditions if any identified
        conditions = lung_data['result'].get('potential_conditions', [])
        if conditions and conditions != ["No specific respiratory conditions identified"]:
            trend_analysis += f"Conditions: {', '.join(conditions)}\n"

        trend_analysis += f"Last measured: {lung_data['timestamp'][:10]}\n\n"
        has_trends = True

    if has_trends:
        trend_analysis += "**Summary**:\n"
        trend_analysis += "I'm tracking your health data over time. As you continue to provide measurements, I'll be able to show you trends and progress toward your health goals.\n\n"
        trend_analysis += "Would you like to update any of your health measurements today?"
        return trend_analysis

    return ""

def process_kidney_function(data: Dict[str, float]) -> Dict:
    """Analyze kidney function data using the updated tool"""
    try:
        logger.info(f"Processing kidney function data: {json.dumps(data)}")
        # The updated kidney_function_analysis_tool returns a more comprehensive result
        # that includes analysis, overall_health, confidence_level, missing_parameters, and recommendations
        result = kidney_function_analysis_tool(data)

        # No need to format the output as the tool now returns a well-structured result
        return result
    except Exception as e:
        error_msg = f"Failed to process kidney function: {str(e)}"
        logger.error(error_msg)
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

def process_lung_capacity(data: Dict[str, Any]) -> Dict:
    """Analyze lung capacity data using the lung capacity analyzer tool"""
    try:
        logger.info(f"Processing lung capacity data: {json.dumps(data)}")
        # Format data for the tool
        data_json = json.dumps({"data": data})

        # Use the lung capacity analyzer tool
        result_json = lung_capacity_analyzer_tool.func(data_json)
        result = json.loads(result_json)

        logger.info(f"Lung capacity analysis result: {json.dumps(result)}")
        return result
    except Exception as e:
        error_msg = f"Failed to process lung capacity data: {str(e)}"
        logger.error(error_msg)
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

def process_test_results(data: Dict[str, Any]) -> Dict:
    """Interpret test results using the test results interpreter tool"""
    try:
        logger.info(f"Processing test results data: {json.dumps(data)}")
        print(f"TEST RESULTS DATA: {json.dumps(data, indent=2)}")

        # Convert data to JSON string for the tool
        data_json = json.dumps(data)

        # Use the enhanced test results interpreter tool
        from tools.tools_test_results import interpret_test_results
        result_json = interpret_test_results(data_json)
        result = json.loads(result_json)

        logger.info(f"Test results interpretation: {json.dumps(result)}")
        return result
    except Exception as e:
        error_msg = f"Failed to process test results: {str(e)}"
        logger.error(error_msg)
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

def process_realtime_health_score(data: Dict[str, Any]) -> Dict:
    """Process real-time health score data using the enhanced HealthScoreAnalysisTool"""
    try:
        logger.info(f"Processing real-time health score data: {json.dumps(data)}")

        # Process data to handle "Unknown" values
        processed_data = {}
        for key, value in data.items():
            # Skip "Unknown" values
            if value == "Unknown":
                continue
            # Skip None values
            elif value is None or value == "" or value == "null":
                continue
            # Handle test results with string values
            elif key in ["Malaria", "Widal Test", "Hepatitis B", "Hiv"]:
                processed_data[key] = value
            # Handle integer values (Systolic, Diastolic)
            elif key in ["Systolic", "Diastolic"]:
                try:
                    processed_data[key] = int(value)
                except (ValueError, TypeError):
                    # If conversion fails, keep the original value
                    processed_data[key] = value
            # Convert other numeric values to float
            else:
                try:
                    processed_data[key] = float(value)
                except (ValueError, TypeError):
                    # If conversion fails, keep the original value
                    processed_data[key] = value

        # Initialize the enhanced health score tool
        health_tool = HealthScoreAnalysisTool()

        # Process the health data
        result = health_tool.generate_report(processed_data)

        logger.info(f"Real-time health score result: {json.dumps(result)}")
        return result
    except Exception as e:
        error_msg = f"Failed to process real-time health score: {str(e)}"
        logger.error(error_msg)
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

def check_health_alerts(user_key) -> str:
    """Check for potential health issues based on user's health data"""
    if user_key not in user_health_data:
        return ""

    alerts = []
    user_data = user_health_data[user_key]

    # Check vital signs for critical values
    if "vital_signs" in user_data:
        vital_data = user_data["vital_signs"]["data"]

        # Check blood pressure
        if "Systolic" in vital_data and "Diastolic" in vital_data:
            systolic = vital_data["Systolic"]
            diastolic = vital_data["Diastolic"]

            if isinstance(systolic, (int, float)) and isinstance(diastolic, (int, float)):
                if systolic > 180 or diastolic > 120:
                    alerts.append("🚨 **CRITICAL**: Your blood pressure readings indicate hypertensive crisis. Seek immediate medical attention.")
                elif systolic > 140 or diastolic > 90:
                    alerts.append("⚠️ Your blood pressure readings indicate hypertension. Consider consulting with your healthcare provider.")

        # Check glucose
        if "Glucose" in vital_data:
            glucose = vital_data["Glucose"]
            if isinstance(glucose, (int, float)):
                if glucose > 200:
                    alerts.append("⚠️ Your glucose level is significantly elevated. This may indicate diabetes or prediabetes. Consider consulting with your healthcare provider.")
                elif glucose < 70:
                    alerts.append("⚠️ Your glucose level is low. This may indicate hypoglycemia. Consider consulting with your healthcare provider.")

        # Check oxygen saturation
        if "Spo2" in vital_data:
            spo2 = vital_data["Spo2"]
            if isinstance(spo2, (int, float)) and spo2 < 92:
                alerts.append("⚠️ Your oxygen saturation is below normal levels. This may indicate respiratory issues. Consider consulting with your healthcare provider.")

    # Check health score for concerning values
    if "health_score" in user_data:
        score = user_data["health_score"]["result"].get("Total Score", 0)
        if score < 50:
            alerts.append("⚠️ Your overall health score is in the 'Poor' range. Consider scheduling a comprehensive health check-up.")

    # Check real-time health score for concerning values
    if "realtime_health_score" in user_data:
        rt_score = user_data["realtime_health_score"]["result"].get("Total Score", 0)
        if rt_score < 50:
            alerts.append("⚠️ Your real-time health score is in the 'Poor' range. Several health metrics need attention.")

        # Check category scores
        if "Category Scores" in user_data["realtime_health_score"]["result"]:
            category_scores = user_data["realtime_health_score"]["result"]["Category Scores"]
            for category, score in category_scores.items():
                if score < 40:
                    alerts.append(f"⚠️ Your {category} score is critically low at {score}%. This area requires immediate attention.")

    # Check kidney function
    if "kidney_function" in user_data:
        kidney_health = user_data["kidney_function"]["result"].get("overall_health", "")
        if "poor" in kidney_health.lower() or "concerning" in kidney_health.lower():
            alerts.append("⚠️ Your kidney function test indicates potential issues. Consider consulting with a nephrologist.")

    # Check lipid profile
    if "lipid_profile" in user_data:
        ascvd_risk = user_data["lipid_profile"]["result"].get("ascvd_risk", "")
        if "high" in ascvd_risk.lower():
            alerts.append("⚠️ Your lipid profile indicates a high cardiovascular risk. Consider consulting with your healthcare provider about strategies to reduce this risk.")

    # Check lung capacity
    if "lung_capacity" in user_data:
        risk_level = user_data["lung_capacity"]["result"].get("respiratory_risk_level", "")
        conditions = user_data["lung_capacity"]["result"].get("potential_conditions", [])

        if "high" in risk_level.lower():
            alerts.append("⚠️ Your lung capacity test indicates a high respiratory risk. Consider consulting with a pulmonologist.")

        if any("COPD" in condition for condition in conditions):
            alerts.append("⚠️ Your lung function test shows patterns consistent with COPD. Follow up with a healthcare provider for proper management.")

        if any("asthma" in condition.lower() for condition in conditions):
            alerts.append("⚠️ Your lung function test shows patterns consistent with asthma. Consider consulting with an allergist or pulmonologist.")

    if alerts:
        return "\n\n**Health Alerts**\n" + "\n".join(alerts)

    return ""

def handle_device_recommendations_intent(user_key: tuple, query_lower: str) -> str:
    """Handle device recommendations intent - simple TurboMedics redirect"""
    try:
        # Create a simple response with TurboMedics link
        response = "🛒 **Health Monitoring Devices**\n\n"

        # Add personalized introduction based on query
        if any(word in query_lower for word in ["buy", "purchase", "shopping", "get", "need"]):
            response += "For purchasing health monitoring devices, "
        elif any(word in query_lower for word in ["recommend", "suggest", "advice"]):
            response += "For health device recommendations, "
        elif any(word in query_lower for word in ["monitor", "track", "measure"]):
            response += "For health monitoring devices, "
        else:
            response += "For health devices, "

        response += "I recommend visiting the **[TurboMedics Store](https://www.turbomedics.com/products)**.\n\n"

        # Add brief description
        response += "TurboMedics offers a comprehensive selection of:\n"
        response += "• Blood pressure monitors\n"
        response += "• Glucose meters\n"
        response += "• Pulse oximeters\n"
        response += "• Digital thermometers\n"
        response += "• Smart scales\n"
        response += "• Heart rate monitors\n"
        response += "• And many more health tracking devices\n\n"

        # Add benefits
        response += "**Why TurboMedics?**\n"
        response += "✅ Clinically validated accuracy\n"
        response += "✅ Easy-to-use devices\n"
        response += "✅ Competitive pricing\n"
        response += "✅ Expert customer support\n\n"

        response += "Visit **[TurboMedics.com/products](https://www.turbomedics.com/products)** to explore their full catalog!"

        return response

    except Exception as e:
        logger.error(f"Error in device recommendations handler: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return ""

@sync_cached(cache_instance=query_response_cache, ttl=3600)
def process_agent_query_cached(query: str, patient_id: str, session_id: str, model_name: str, has_health_data: bool = False) -> Dict:
    """Cached version of agent query processing for common queries"""
    return process_agent_query_internal(query, patient_id, session_id, model_name, has_health_data)

def process_agent_query(query: str, patient_id: str, session_id: str, model_name: str) -> Dict:
    """Process a query through the agent, detecting intent and using appropriate tools"""
    try:
        user_key = (patient_id, session_id)

        # Check if user has health data (affects caching)
        has_health_data = user_key in user_health_data and bool(user_health_data[user_key])

        # Use cached version for common queries without health data dependency
        if is_query_cacheable(query) and not has_health_data:
            logger.info(f"Using cached query processing for: {query[:50]}...")
            return process_agent_query_cached(query, patient_id, session_id, model_name, has_health_data)

        # Use direct processing for personalized queries
        return process_agent_query_internal(query, patient_id, session_id, model_name, has_health_data)

    except Exception as e:
        error_msg = f"Failed to process agent query: {str(e)}"
        logger.error(error_msg)
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise Exception(error_msg)

def process_agent_query_internal(query: str, patient_id: str, session_id: str, model_name: str, has_health_data: bool = False) -> Dict:
    """Internal query processing logic"""
    try:
        user_key = (patient_id, session_id)

        # Get relevant context from vector store (cached)
        context = retrieve_context_cache(query, model_name)
        logger.info(f"Retrieved context: {context[:100]}...")

        # Detect intent from the query
        query_lower = query.lower()

        # Prepare system prompt based on detected intent
        system_prompt = """You are Dr. Deuce, a certified and authorized medical assistant. You assist users by analyzing their health data, monitoring vitals, and providing health consultations.

IMPORTANT INSTRUCTIONS:
- When giving recommendations or health advice, ALWAYS use the user's actual health data if available
- Personalize your responses based on their specific health metrics rather than giving generic advice
- When asked for exercise plans, provide specific, detailed recommendations with exercises, duration, frequency, and intensity
- When asked for meal plans, provide specific foods, portions, and meal timing
- Always provide comprehensive, helpful responses - not just brief introductions
- Use the user's health score, vital signs, and test results to tailor your recommendations
- Be specific and actionable in your advice"""

        # Add context to the system prompt
        if context:
            system_prompt += f"\n\nRELEVANT KNOWLEDGE BASE INFORMATION:\n{context}\n\nIMPORTANT: Use the above knowledge base information along with the user's health data to provide comprehensive, evidence-based recommendations."
            logger.info(f"Added {len(context)} characters of vector store context to system prompt")

        # Check for any health alerts that should be brought to the user's attention
        health_alerts = check_health_alerts(user_key)
        if health_alerts:
            system_prompt += f"\n\nIMPORTANT HEALTH ALERTS: {health_alerts}\nMake sure to mention these alerts to the user if they are relevant to their query."

        # Always include user health data in the system prompt
        health_data_context = ""
        logger.info(f"Checking health data for user_key: {user_key}")
        logger.info(f"Available user keys in health data: {list(user_health_data.keys())}")

        if user_key in user_health_data:
            logger.info(f"Found health data for user_key: {user_key}")
            logger.info(f"Available health data types: {list(user_health_data[user_key].keys())}")
            # Add a header for the health data section
            health_data_context += "\n\nUser's Health Data Summary:\n"

            # Add vital signs data if available
            if "vital_signs" in user_health_data[user_key]:
                vital_data = user_health_data[user_key]["vital_signs"]
                health_data_context += "\n📊 Vital Signs:\n"
                health_data_context += f"Test date: {vital_data['timestamp'][:10]}\n"

                # Add vital signs
                for key, value in vital_data['data'].items():
                    health_data_context += f"- {key}: {value}\n"

                # Add alerts if any
                if "alerts" in vital_data['result'] and vital_data['result']['alerts']:
                    health_data_context += f"Alerts: {vital_data['result']['alerts']}\n"

            # Add health score data if available
            if "health_score" in user_health_data[user_key]:
                score_data = user_health_data[user_key]["health_score"]
                health_data_context += "\n🏆 Health Score:\n"
                health_data_context += f"Test date: {score_data['timestamp'][:10]}\n"
                health_data_context += f"Total Score: {score_data['result'].get('Total Score', 'Unknown')} Health Status: {score_data['result'].get('Health Status', 'Unknown')}\n\n"

                # Add vitals needing improvement
                if "Vitals Needing Improvement" in score_data['result']:
                    vitals = score_data['result']['Vitals Needing Improvement']
                    health_data_context += "Vitals Needing Improvement:\n"
                    if isinstance(vitals, list):
                        if vitals == ["None"]:
                            health_data_context += "None\n\n"
                        else:
                            for vital in vitals:
                                health_data_context += f"- {vital}\n"
                            health_data_context += "\n"
                    else:
                        health_data_context += f"{vitals}\n\n"

                # Add improvement tips
                if "Improvement Tips" in score_data['result']:
                    tips = score_data['result']['Improvement Tips']
                    health_data_context += "Improvement Tips:\n"
                    if isinstance(tips, list):
                        if not tips:
                            health_data_context += "None\n"
                        else:
                            for tip in tips:
                                health_data_context += f"- {tip}\n"
                    else:
                        health_data_context += f"{tips}\n"

            # Add real-time health score data if available
            if "realtime_health_score" in user_health_data[user_key]:
                rt_score_data = user_health_data[user_key]["realtime_health_score"]
                health_data_context += "\n🏆 Real-Time Health Score:\n"
                health_data_context += f"Test date: {rt_score_data['timestamp'][:10]}\n"
                health_data_context += f"Total Score: {rt_score_data['result'].get('Total Score', 'Unknown')} Health Status: {rt_score_data['result'].get('Health Status', 'Unknown')}\n\n"

                # Add raw health data with detailed Widal Test format
                if "data" in rt_score_data:
                    health_data_context += "Raw Health Data:\n"
                    for key, value in rt_score_data['data'].items():
                        if key == "Widal Test" and isinstance(value, dict):
                            health_data_context += f"- {key}:\n"
                            for antibody, result in value.items():
                                health_data_context += f"  • {antibody}: {result}\n"
                        else:
                            health_data_context += f"- {key}: {value}\n"
                    health_data_context += "\n"

                # Add vitals needing improvement
                if "Vitals Needing Improvement" in rt_score_data['result']:
                    vitals = rt_score_data['result']['Vitals Needing Improvement']
                    health_data_context += "Vitals Needing Improvement:\n"
                    if isinstance(vitals, list):
                        if vitals == ["None"]:
                            health_data_context += "None\n\n"
                        else:
                            for vital in vitals:
                                health_data_context += f"- {vital}\n"
                            health_data_context += "\n"
                    else:
                        health_data_context += f"{vitals}\n\n"

                # Add improvement tips
                if "Improvement Tips" in rt_score_data['result']:
                    tips = rt_score_data['result']['Improvement Tips']
                    health_data_context += "Improvement Tips:\n"
                    if isinstance(tips, list):
                        if not tips:
                            health_data_context += "None\n"
                        else:
                            for tip in tips:
                                health_data_context += f"- {tip}\n"
                    else:
                        health_data_context += f"{tips}\n"

            # Add kidney function data if available
            if "kidney_function" in user_health_data[user_key]:
                kidney_data = user_health_data[user_key]["kidney_function"]
                health_data_context += "\n🧪 Kidney Function:\n"
                health_data_context += f"Test date: {kidney_data['timestamp'][:10]}\n"
                health_data_context += f"Overall health: {kidney_data['result'].get('overall_health', 'Unknown')}\n"
                health_data_context += f"Confidence level: {kidney_data['result'].get('confidence_level', 'Unknown')}\n"

                # Add analysis items
                analysis_items = kidney_data['result'].get('analysis', [])
                if analysis_items:
                    health_data_context += "Analysis:\n"
                    if isinstance(analysis_items, list):
                        for item in analysis_items:
                            health_data_context += f"- {item}\n"
                    else:
                        health_data_context += f"{analysis_items}\n"

                # Add recommendations if available
                if "recommendations" in kidney_data['result'] and kidney_data['result']['recommendations']:
                    health_data_context += "Recommendations:\n"
                    for rec in kidney_data['result']['recommendations']:
                        health_data_context += f"- {rec}\n"

            # Add lipid profile data if available
            if "lipid_profile" in user_health_data[user_key]:
                lipid_data = user_health_data[user_key]["lipid_profile"]
                health_data_context += "\n💉 Lipid Profile:\n"
                health_data_context += f"Test date: {lipid_data['timestamp'][:10]}\n"

                # Add classification
                classification = lipid_data['result'].get('classification', {})
                if classification:
                    health_data_context += "Classification:\n"
                    for component, level in classification.items():
                        health_data_context += f"- {component.replace('_', ' ').title()}: {level.title()}\n"

                # Add risk assessment
                health_data_context += f"ASCVD Risk: {lipid_data['result'].get('ascvd_risk', 'Unknown')}\n"

                # Add parameter explanations if available
                if "parameter_explanations" in lipid_data['result'] and lipid_data['result']['parameter_explanations']:
                    health_data_context += "Parameter Explanations:\n"
                    for param in lipid_data['result']['parameter_explanations'][:2]:  # Limit to 2 for brevity
                        health_data_context += f"- {param['parameter']}: {param['status_explanation']}\n"

                # Add diet plan information if available
                if "diet_plan" in lipid_data['result'] and lipid_data['result']['diet_plan']:
                    diet_plan = lipid_data['result']['diet_plan']
                    health_data_context += f"Diet Plan: {diet_plan.get('title', 'Heart-Healthy Diet Plan')}\n"
                    health_data_context += f"Overview: {diet_plan.get('overview', '')}\n"

                # Add cardiac health plan information if available
                if "cardiac_health_plan" in lipid_data['result'] and lipid_data['result']['cardiac_health_plan']:
                    cardiac_plan = lipid_data['result']['cardiac_health_plan']
                    health_data_context += f"Cardiac Health Plan: {cardiac_plan.get('title', 'Personalized Cardiac Health Plan')}\n"
                    health_data_context += f"Follow-up: {cardiac_plan.get('follow_up', '')}\n"

                # Add recommendations if available
                if "recommendations" in lipid_data['result'] and lipid_data['result']['recommendations']:
                    health_data_context += "Recommendations:\n"
                    for rec in lipid_data['result']['recommendations'][:3]:  # Limit to 3 for brevity
                        health_data_context += f"- {rec}\n"

            # Add reproductive health cycle data if available
            if "reproductive_health_cycle" in user_health_data[user_key]:
                cycle_data = user_health_data[user_key]["reproductive_health_cycle"]
                health_data_context += "\n🩸 Reproductive Health (Cycle):\n"
                health_data_context += f"Test date: {cycle_data['timestamp'][:10]}\n"
                if "next_prediction" in cycle_data['result'] and cycle_data['result']['next_prediction']:
                    pred = cycle_data['result']['next_prediction']
                    health_data_context += f"Next period: {pred.get('Next Period Start', 'N/A')}\n"
                    health_data_context += f"Ovulation window: {pred.get('Ovulation Window', 'N/A')}\n"
                if "recommendations" in cycle_data['result'] and cycle_data['result']['recommendations']:
                    health_data_context += "Recommendations:\n"
                    for rec in cycle_data['result']['recommendations'][:3]:  # Limit to 3 for brevity
                        health_data_context += f"- {rec}\n"

            # Add pregnancy monitoring data if available
            if "pregnancy_monitoring" in user_health_data[user_key]:
                pregnancy_data = user_health_data[user_key]["pregnancy_monitoring"]
                health_data_context += "\n🤰 Pregnancy Monitoring:\n"
                health_data_context += f"Test date: {pregnancy_data['timestamp'][:10]}\n"
                health_data_context += f"Gestational Age: {pregnancy_data['result'].get('Gestational Age', 'N/A')}\n"
                if "Expected Delivery Window" in pregnancy_data['result']:
                    edd = pregnancy_data['result']['Expected Delivery Window']
                    health_data_context += f"Expected Delivery: {edd.get('Start', 'N/A')} to {edd.get('End', 'N/A')}\n"
                if "recommendations" in pregnancy_data['result'] and pregnancy_data['result']['recommendations']:
                    health_data_context += "Recommendations:\n"
                    for rec in pregnancy_data['result']['recommendations'][:3]:  # Limit to 3 for brevity
                        health_data_context += f"- {rec}\n"

            # Add postpartum health data if available
            if "postpartum_health" in user_health_data[user_key]:
                postpartum_data = user_health_data[user_key]["postpartum_health"]
                health_data_context += "\n🤱 Postpartum Health:\n"
                health_data_context += f"Test date: {postpartum_data['timestamp'][:10]}\n"
                health_data_context += f"Days since delivery: {postpartum_data['result'].get('Days Since Delivery', 'N/A')}\n"
                if "Flags" in postpartum_data['result'] and postpartum_data['result']['Flags']:
                    health_data_context += f"Health flags: {len(postpartum_data['result']['Flags'])} items to monitor\n"
                if "recommendations" in postpartum_data['result'] and postpartum_data['result']['recommendations']:
                    health_data_context += "Recommendations:\n"
                    for rec in postpartum_data['result']['recommendations'][:3]:  # Limit to 3 for brevity
                        health_data_context += f"- {rec}\n"

            # Add instructions for the agent to use this data
            health_data_context += "\nIMPORTANT: Always use the above health data when providing recommendations or answering health-related questions. Personalize your responses based on this data."
            health_data_context += "\n\nWhen asked for exercise plans, consider the user's:"
            health_data_context += "\n- Current health score and status"
            health_data_context += "\n- Vital signs (blood pressure, heart rate, weight)"
            health_data_context += "\n- Any areas needing improvement"
            health_data_context += "\n- Provide specific exercises, duration, frequency, and intensity levels"
            health_data_context += "\n- Include both cardio and strength training recommendations"
        else:
            logger.info(f"No health data found for user_key: {user_key}")
            health_data_context = "\n\nNo previous health data found for this user. When providing health advice, ask the user to take a health assessment first or provide their current health information."

        # Add health data context to system prompt if available
        if health_data_context:
            system_prompt += f"\n\nUser's health data for reference:{health_data_context}"
            logger.info(f"Added health data context to system prompt for user {user_key}")

        # Initialize chat history if first interaction
        if user_key not in chat_histories:
            chat_histories[user_key] = [{"role": "system", "content": system_prompt}]
            chat_titles[user_key] = generate_chat_title(query)
        else:
            # Update system prompt with latest health data context
            chat_histories[user_key][0]["content"] = system_prompt

        # Add user query to history
        chat_histories[user_key].append({"role": "user", "content": query})

        # Keep chat history within limit
        if len(chat_histories[user_key]) > MAX_HISTORY_LENGTH:
            # Keep the system message and the most recent messages
            system_message = chat_histories[user_key][0]
            chat_histories[user_key] = [system_message] + chat_histories[user_key][-(MAX_HISTORY_LENGTH-1):]

        # Create summarized chat history for AI model (max 200 messages)
        # This reduces processing time and avoids timeouts
        summarized_history = summarize_chat_history(chat_histories[user_key], max_messages=50)

        logger.info(f"Chat history: Full={len(chat_histories[user_key])}, Summarized={len(summarized_history)} messages")

        # Get response from Ollama using summarized history
        response = ollama.chat(model=model_name, messages=summarized_history)
        model_response = response["message"]["content"]

        # Enhanced intent detection with more sophisticated pattern matching
        tool_response = ""
        tools_used = []
        # Define intent patterns with weights and related terms
        intent_patterns = {
            "recommendation": {
                "keywords": ["recommendation", "advice", "suggest", "tips", "what should i do", "help me", "guidance",
                            "what can i do", "how can i improve", "how to", "best way to"],
                "weight": 1.0,
                "handler": "handle_recommendation_intent"
            },
            "health_score": {
                "keywords": ["health score", "analyze my health", "health analysis", "overall health", "how healthy am i",
                            "health assessment", "evaluate my health", "health status", "health check"],
                "weight": 1.0,
                "handler": "handle_health_score_intent"
            },
            "realtime_health_score": {
                "keywords": ["real-time health score", "realtime health score", "real time health score", "dynamic health score",
                            "holistic health score", "comprehensive health score", "combined health score",
                            "lifestyle and vitals", "complete health picture", "health dashboard"],
                "weight": 1.2,  # Higher weight for this specialized intent
                "handler": "handle_realtime_health_score_intent"
            },
            "vital_signs": {
                "keywords": ["vital signs", "monitor vitals", "check vitals", "blood pressure", "heart rate",
                            "temperature", "oxygen", "spo2", "pulse", "vitals"],
                "weight": 1.0,
                "handler": "handle_vital_signs_intent"
            },
            "kidney_function": {
                "keywords": ["kidney function", "kidney test", "renal function", "kidney health", "creatinine",
                            "egfr", "bun", "kidney disease", "kidney problems"],
                "weight": 1.0,
                "handler": "handle_kidney_function_intent"
            },
            "lipid_profile": {
                "keywords": ["lipid profile", "cholesterol test", "lipid test", "cholesterol", "triglycerides",
                            "hdl", "ldl", "heart health", "cardiovascular", "cardiac health", "heart disease risk",
                            "heart diet", "heart-healthy diet", "cardiac risk", "ascvd risk", "heart attack risk"],
                "weight": 1.0,
                "handler": "handle_lipid_profile_intent"
            },
            "lung_capacity": {
                "keywords": ["lung capacity", "respiratory", "breathing", "spirometry", "fev1", "fvc", "copd",
                            "asthma", "pulmonary", "lung function", "lung health", "breath test", "shortness of breath",
                            "breathing difficulty", "lung disease", "respiratory health"],
                "weight": 1.0,
                "handler": "handle_lung_capacity_intent"
            },
            "liver_function": {
                "keywords": ["liver function", "liver test", "hepatic function", "liver health", "alt", "ast", "alp",
                            "bilirubin", "liver enzymes", "hepatitis", "liver disease", "liver panel", "lft",
                            "liver function test", "hepatic panel", "liver profile", "liver screening"],
                "weight": 1.0,
                "handler": "handle_liver_function_intent"
            },
            "health_consultation": {
                "keywords": ["health consultation", "consult", "medical advice", "talk to doctor", "professional opinion",
                            "medical consultation", "doctor advice", "clinical advice", "care plan", "care coordination",
                            "screening", "test schedule", "recommended tests", "proactive care", "preventive care",
                            "preventative care", "health plan"],
                "weight": 1.0,
                "handler": "handle_consultation_intent"
            },
            "appointment_booking": {
                "keywords": ["appointment", "schedule", "book appointment", "book", "schedule appointment",
                            "checkup", "follow-up", "follow up", "doctor visit", "specialist", "when should i see a doctor",
                            "medical appointment", "see a doctor", "visit doctor", "make appointment", "need appointment",
                            "want appointment", "book consultation", "schedule consultation", "reserve appointment"],
                "weight": 1.2,  # Higher weight for direct appointment requests
                "handler": "handle_appointment_booking_intent"
            },

            "health_trends": {
                "keywords": ["trends", "progress", "changes", "over time", "improving", "getting better", "getting worse",
                            "track", "history", "compare", "improvement", "deterioration"],
                "weight": 1.0,
                "handler": "handle_health_trends_intent"
            },
            "device_recommendations": {
                "keywords": ["device", "devices", "gadget", "gadgets", "health device", "health devices", "health gadget",
                            "health gadgets", "monitor", "monitoring device", "wearable", "wearables", "smart device",
                            "smart watch", "fitness tracker", "health tracker", "connected device", "connected health",
                            "recommend device", "suggest device", "what device", "which device", "home monitoring",
                            "turbomedics", "turbo medics", "health equipment", "medical device", "medical equipment",
                            "blood pressure monitor", "glucose meter", "pulse oximeter", "thermometer", "scale",
                            "health monitoring", "vital signs monitor", "home health", "remote monitoring",
                            "device recommendation", "device suggestions", "monitoring tools", "health tools",
                            "what should i buy", "what to buy", "shopping", "purchase", "buy device", "get device",
                            "need device", "want device", "help me choose", "which monitor", "best device",
                            "device for", "monitor for", "track my", "measure my", "check my"],
                "weight": 1.0,
                "handler": "handle_device_recommendations_intent"
            },
            "chronic_tracker": {
                "keywords": ["chronic tracker", "track condition", "monitor condition", "track diabetes", "track hypertension",
                            "track asthma", "track heart disease", "track kidney disease", "diabetes tracking",
                            "hypertension tracking", "asthma tracking", "heart disease tracking", "kidney disease tracking",
                            "chronic condition", "chronic management", "manage diabetes", "manage hypertension",
                            "manage asthma", "manage heart disease", "manage kidney disease", "chronic care",
                            "diabetes management", "blood sugar tracking", "glucose monitoring", "blood pressure tracking",
                            "peak flow tracking", "track my condition", "monitor my health", "track my chronic condition"],
                "weight": 1.0,
                "handler": "handle_chronic_tracker"
            },
            "reproductive_health": {
                "keywords": ["reproductive health", "menstrual cycle", "period", "menstruation", "cycle tracking",
                            "ovulation", "fertility", "reproductive", "women's health", "feminine health",
                            "period tracker", "cycle prediction", "menstrual health", "reproductive wellness",
                            "track my period", "track my cycle", "when is my next period", "ovulation window",
                            "fertile window", "cycle length", "period symptoms", "menstrual symptoms"],
                "weight": 1.0,
                "handler": "handle_reproductive_health_intent"
            },
            "pregnancy_monitoring": {
                "keywords": ["pregnancy", "pregnant", "pregnancy monitoring", "pregnancy tracking", "gestational age",
                            "pregnancy symptoms", "prenatal", "prenatal care", "pregnancy health", "expecting",
                            "baby", "fetal", "trimester", "pregnancy advice", "pregnancy tips", "pregnancy support",
                            "morning sickness", "pregnancy complications", "delivery", "due date", "lmp",
                            "last menstrual period", "pregnancy week", "pregnancy progress"],
                "weight": 1.0,
                "handler": "handle_pregnancy_monitoring_intent"
            },
            "postpartum_health": {
                "keywords": ["postpartum", "postpartum health", "postpartum recovery", "after delivery", "after birth",
                            "postpartum care", "new mom", "new mother", "breastfeeding", "nursing", "baby care",
                            "postpartum depression", "postpartum healing", "recovery after birth", "maternal health",
                            "postpartum support", "postpartum wellness", "feeding baby", "baby feeding",
                            "postpartum symptoms", "postpartum advice", "postpartum tips"],
                "weight": 1.0,
                "handler": "handle_postpartum_health_intent"
            },
            "yes_confirmation": {
                "keywords": ["yes", "sure", "okay", "ok", "yep", "yeah", "proceed", "let's do it", "go ahead"],
                "weight": 1.5,  # Higher weight for confirmation
                "handler": "handle_confirmation"
            },
            "no_confirmation": {
                "keywords": ["no", "nope", "nah", "not now", "maybe later", "not interested", "skip"],
                "weight": 1.5,  # Higher weight for confirmation
                "handler": "handle_decline"
            }
        }

        # Detect intents with scoring
        detected_intents = {}
        for intent, config in intent_patterns.items():
            score = 0
            for keyword in config["keywords"]:
                if keyword in query_lower:
                    # Add score based on keyword match and weight
                    score += config["weight"]
                    # Add extra score for exact matches or standalone keywords
                    if keyword == query_lower.strip() or f" {keyword} " in f" {query_lower} ":
                        score += 0.5

            if score > 0:
                detected_intents[intent] = score

        # Sort intents by score
        sorted_intents = sorted(detected_intents.items(), key=lambda x: x[1], reverse=True)

        # Handle the highest scoring intent
        if sorted_intents:
            primary_intent = sorted_intents[0][0]
            logger.info(f"Detected primary intent: {primary_intent} with score {sorted_intents[0][1]}")

            # If user has health data and is asking for recommendations/advice,
            # reduce intent interference to let the LLM handle it naturally
            if (user_key in user_health_data and user_health_data[user_key] and
                primary_intent in ["health_score", "vital_signs", "kidney_function", "lipid_profile", "liver_function"] and
                any(word in query_lower for word in ["recommend", "advice", "suggest", "meal", "diet", "food", "plan", "help", "what should"])):
                logger.info(f"User has health data and is asking for recommendations - reducing intent interference")
                primary_intent = "recommendation"  # Override with recommendation intent

            # Check for confirmation intent (special case)
            if primary_intent == "yes_confirmation":
                # Look for the most recent intent in chat history
                previous_intent = get_previous_intent(user_key)
                if previous_intent:
                    primary_intent = previous_intent
                    logger.info(f"Confirmation detected, using previous intent: {primary_intent}")

                    # Special handling for health consultation confirmation
                    if previous_intent == "health_consultation" and user_key in user_health_data and user_health_data[user_key]:
                        # Add to chat history
                        chat_histories[user_key].append({"role": "user", "content": "Yes, I'd like a comprehensive health consultation."})

                        # instead of awaiting it in this synchronous context
                        consultation_input = {
                            "user_id": f"{user_key[0]}_{user_key[1]}",  # Convert tuple to string for the tool
                            "health_data": user_health_data[user_key]
                        }

                        # Run the comprehensive health consultation tool directly
                        consultation_result_json = automated_health_consultation_tool.func(json.dumps(consultation_input))
                        consultation_result = json.loads(consultation_result_json)

                    # Special handling for device recommendations confirmation
                    elif previous_intent == "device_recommendations":
                        # Add to chat history
                        chat_histories[user_key].append({"role": "user", "content": "Yes, I'd like to see device recommendations."})

                        # Simple TurboMedics redirect
                        summary = "Great! For health monitoring devices, I recommend visiting **[TurboMedics](https://www.turbomedics.com/products)**.\n\n"
                        summary += "They have a comprehensive selection of health tracking devices including:\n"
                        summary += "• Blood pressure monitors\n"
                        summary += "• Glucose meters\n"
                        summary += "• Pulse oximeters\n"
                        summary += "• Digital thermometers\n"
                        summary += "• Smart scales\n"
                        summary += "• And much more!\n\n"
                        summary += "Visit **[TurboMedics.com/products](https://www.turbomedics.com/products)** to explore their full catalog!"

                        # Add assistant message
                        chat_histories[user_key].append({"role": "assistant", "content": summary})

                        # Return the simple response
                        return {
                            "response": summary,
                            "chat_title": chat_titles[user_key],
                            "chat_history": chat_histories[user_key]
                        }

                # Check if the last assistant message contains a device recommendation prompt (from health analysis)
                elif user_key in chat_histories and len(chat_histories[user_key]) > 1:
                    last_assistant_message = None
                    for message in reversed(chat_histories[user_key]):
                        if message["role"] == "assistant":
                            last_assistant_message = message["content"]
                            break

                    # Check if the last message contains a device recommendation prompt
                    if last_assistant_message and "turbomedics" in last_assistant_message.lower():
                        # Handle device recommendation confirmation from health analysis
                        chat_histories[user_key].append({"role": "user", "content": "Yes, I'd like to see health monitoring devices."})

                        # Simple TurboMedics redirect
                        response_message = "Perfect! Visit **[TurboMedics](https://www.turbomedics.com/products)** for a comprehensive selection of health monitoring devices.\n\n"
                        response_message += "They offer:\n"
                        response_message += "✅ Clinically validated devices\n"
                        response_message += "✅ Easy-to-use technology\n"
                        response_message += "✅ Competitive pricing\n"
                        response_message += "✅ Expert customer support\n\n"
                        response_message += "Visit **[TurboMedics.com/products](https://www.turbomedics.com/products)** to start shopping!"

                        chat_histories[user_key].append({"role": "assistant", "content": response_message})

                        return {
                            "response": response_message,
                            "chat_title": chat_titles[user_key],
                            "chat_history": chat_histories[user_key]
                        }

            # Check for decline intent (special case)
            elif primary_intent == "no_confirmation":
                # Check if the last assistant message contains a device recommendation prompt
                if user_key in chat_histories and len(chat_histories[user_key]) > 1:
                    last_assistant_message = None
                    for message in reversed(chat_histories[user_key]):
                        if message["role"] == "assistant":
                            last_assistant_message = message["content"]
                            break

                    # Check if the last message contains a device recommendation prompt
                    if last_assistant_message and "turbomedics" in last_assistant_message.lower():
                        # Handle device recommendation decline
                        chat_histories[user_key].append({"role": "user", "content": "No, I don't need device recommendations right now."})

                        # Simple acknowledgment
                        response_message = "No problem! If you ever want to explore health monitoring devices in the future, "
                        response_message += "you can always visit **[TurboMedics](https://www.turbomedics.com/products)**. "
                        response_message += "Is there anything else I can help you with regarding your health?"

                        chat_histories[user_key].append({"role": "assistant", "content": response_message})

                        return {
                            "response": response_message,
                            "chat_title": chat_titles[user_key],
                            "chat_history": chat_histories[user_key]
                        }

                # Continue with health consultation formatting if that was the previous intent
                if previous_intent == "health_consultation":
                        # Format the consultation result in a more conversational, human-like way
                        urgency = consultation_result.get("urgency_level", "Low")
                        urgency_emoji = "🟢" if urgency == "Low" else "🟡" if urgency == "Medium" else "🔴" if urgency == "High" else "⚠️"

                        # Start with a friendly greeting
                        formatted_result = "Hi there! I've looked through your health information and here's what I found.\n\n"

                        # Add urgency level with appropriate wording
                        if urgency == "Low":
                            formatted_result += f"{urgency_emoji} Overall, your health indicators look good! There's no immediate cause for concern based on the data I can see.\n\n"
                        elif urgency == "Medium":
                            formatted_result += f"{urgency_emoji} I've noticed a few things that might need some attention. Nothing urgent, but worth discussing with your doctor in the next few weeks.\n\n"
                        else:
                            formatted_result += f"{urgency_emoji} I've found some concerning indicators that should be addressed soon. I'd recommend speaking with a healthcare provider as soon as possible.\n\n"

                        # Add medical advice in a conversational way
                        medical_advice = consultation_result.get("medical_advice", [])
                        if medical_advice:
                            formatted_result += "Here's my advice based on your health data:\n\n"
                            for i, advice in enumerate(medical_advice):
                                # Remove bullet points and make more conversational
                                advice_text = advice
                                if advice.startswith("- "):
                                    advice_text = advice[2:]
                                formatted_result += f"{advice_text}\n\n"

                        # Add specialist recommendations if any
                        specialist_recommendations = consultation_result.get("specialist_recommendations", [])
                        if specialist_recommendations:
                            if len(specialist_recommendations) == 1:
                                formatted_result += f"Based on what I'm seeing, you might benefit from talking to a {specialist_recommendations[0]}.\n\n"
                            else:
                                formatted_result += "Based on your health data, you might benefit from consulting with these specialists:\n\n"
                                for specialist in specialist_recommendations:
                                    formatted_result += f"• A {specialist}\n"
                                formatted_result += "\n"

                        # Add integrated analysis in a more conversational way
                        integrated_analysis = consultation_result.get("integrated_analysis", [])
                        if integrated_analysis:
                            formatted_result += "Let me break down what I'm seeing in your health data:\n\n"
                            for analysis in integrated_analysis:
                                # Make it more conversational
                                if analysis.startswith("Health score:"):
                                    # Keep the emoji if present
                                    emoji_match = re.search(r'(🩸|🍎|🚶‍♀️|[^\w\s])', analysis)
                                    emoji = emoji_match.group(0) + " " if emoji_match else ""
                                    content = analysis.split(":", 1)[1].strip()
                                    formatted_result += f"{emoji}I notice that {content}\n\n"
                                else:
                                    formatted_result += f"{analysis}\n\n"

                        # Add health summary in a more conversational way
                        health_summary = consultation_result.get("health_summary", {})
                        if health_summary:
                            formatted_result += "Here's a quick summary of your health metrics:\n\n"

                            if "health_score" in health_summary:
                                score_data = health_summary["health_score"]
                                score = score_data.get('score', 'N/A')
                                status = score_data.get('status', 'Unknown')
                                formatted_result += f"Your overall health score is {score}, which is considered '{status}'. "

                            if "kidney_function" in health_summary:
                                kidney_data = health_summary["kidney_function"]
                                kidney_health = kidney_data.get('overall_health', 'Unknown')
                                # Remove emoji if present
                                kidney_health = re.sub(r'[^\w\s]', '', kidney_health).strip()
                                formatted_result += f"Your kidney function appears to be {kidney_health.lower()}. "

                            if "lipid_profile" in health_summary:
                                lipid_data = health_summary["lipid_profile"]
                                cv_risk = lipid_data.get('ascvd_risk', 'Unknown')
                                formatted_result += f"Your cardiovascular risk is in the {cv_risk.lower()} range. "

                            if "liver_function" in health_summary:
                                liver_data = health_summary["liver_function"]
                                liver_risk = liver_data.get('risk_level', 'Unknown')
                                formatted_result += f"Your liver function shows {liver_risk.lower()} risk level."

                            formatted_result += "\n\n"

                        # Save consultation result to user health data
                        user_health_data[user_key]["health_consultation"] = {
                            "result": consultation_result,
                            "timestamp": datetime.now().isoformat()
                        }

                        # Create the final result
                        consultation_result = {
                            "consultation": formatted_result,
                            "doctor_visit_recommended": consultation_result.get("doctor_visit_recommended", False),
                            "urgency_level": urgency
                        }

                        # Check if there was an error
                        if "error" in consultation_result:
                            error_message = f"I'm sorry, there was an error processing your health consultation: {consultation_result['error']}"
                            chat_histories[user_key].append({"role": "assistant", "content": error_message})
                            return {
                                "response": error_message,
                                "chat_title": chat_titles[user_key],
                                "chat_history": chat_histories[user_key]
                            }

                        # Return the consultation result
                        return {
                            "response": consultation_result["consultation"],
                            "chat_title": chat_titles[user_key],
                            "chat_history": chat_histories[user_key],
                            "doctor_visit_recommended": consultation_result["doctor_visit_recommended"],
                            "urgency_level": consultation_result["urgency_level"]
                        }



            # Handle recommendation intent
            if primary_intent == "recommendation":
                # Check if user has any health data
                if user_key in user_health_data and user_health_data[user_key]:
                    # Create a personalized recommendation response
                    recommendation_response = "Based on your health data, here are my personalized recommendations:\n\n"

                    # Critical health alerts disabled - removed to prevent urgent medical alerts in chatbot
                    critical_alerts = []
                    urgent_recommendations = []

                    # Check real-time health score for critical issues (alerts disabled)
                    if "realtime_health_score" in user_health_data[user_key]:
                        rt_score_data = user_health_data[user_key]["realtime_health_score"]
                        rt_score = rt_score_data["result"].get("Total Score", 0)

                        # Critical health score alerts disabled
                        # if rt_score < 60:
                        #     critical_alerts.append("🚨 **CRITICAL**: Your real-time health score is critically low. Immediate medical attention recommended.")

                        # Check for concerning test results in the raw data
                        raw_data = rt_score_data.get("data", {})

                        # Widal Test alerts disabled
                        # if "Widal Test" in raw_data and isinstance(raw_data["Widal Test"], dict):
                        #     reactive_count = 0
                        #     reactive_antibodies = []
                        #     for antibody, result in raw_data["Widal Test"].items():
                        #         if result and result.lower() == "reactive":
                        #             reactive_count += 1
                        #             reactive_antibodies.append(antibody)

                        #     if reactive_count > 0:
                        #         critical_alerts.append(f"🚨 **URGENT MEDICAL ATTENTION REQUIRED**: Your Widal Test shows {reactive_count} reactive antibodies ({', '.join(reactive_antibodies)}). This indicates possible typhoid infection and requires immediate medical consultation.")
                        #         urgent_recommendations.append("📞 Contact your healthcare provider immediately to discuss these test results")
                        #         urgent_recommendations.append("🏥 Consider visiting an emergency room or urgent care if you have fever, severe headache, or abdominal pain")
                        #         urgent_recommendations.append("💊 Do not self-medicate - typhoid requires specific antibiotic treatment prescribed by a doctor")

                        # Other concerning test results alerts disabled
                        # concerning_tests = []
                        # if raw_data.get("Hepatitis B") == "Positive":
                        #     concerning_tests.append("Hepatitis B")
                        # if raw_data.get("Hiv") == "Positive":
                        #     concerning_tests.append("HIV")
                        # if raw_data.get("Malaria") == "Positive":
                        #     concerning_tests.append("Malaria")

                        # if concerning_tests:
                        #     critical_alerts.append(f"🚨 **MEDICAL ATTENTION REQUIRED**: Positive results detected for: {', '.join(concerning_tests)}")
                        #     urgent_recommendations.append("🩺 Schedule immediate appointment with your healthcare provider")

                        # Lifestyle score alerts disabled
                        # if "Category Scores" in rt_score_data["result"]:
                        #     lifestyle_score = rt_score_data["result"]["Category Scores"].get("Lifestyle", 100)
                        #     if lifestyle_score == 0:
                        #         critical_alerts.append("⚠️ **LIFESTYLE ALERT**: Your lifestyle score is 0% - this requires immediate attention for your long-term health")

                        # Add structured recommendations (critical alerts disabled)
                        if "Improvement Tips" in rt_score_data["result"] and rt_score_data["result"]["Improvement Tips"]:
                            recommendation_response += "**Real-Time Health Score Recommendations:**\n"
                            tips = rt_score_data["result"]["Improvement Tips"]
                            if isinstance(tips, list):
                                for tip in tips:
                                    if tip.strip():
                                        recommendation_response += f"- {tip.strip()}\n"
                            else:
                                # Handle string format
                                tip_list = tips.split(". ")
                                for tip in tip_list:
                                    if tip.strip():
                                        recommendation_response += f"- {tip.strip()}.\n"
                            recommendation_response += "\n"

                    # Add recommendations from regular health score if available (and no real-time score)
                    elif "health_score" in user_health_data[user_key]:
                        score_data = user_health_data[user_key]["health_score"]
                        if "Improvement Tips" in score_data["result"] and score_data["result"]["Improvement Tips"]:
                            recommendation_response += "**Health Score Recommendations:**\n"
                            tips = score_data["result"]["Improvement Tips"]
                            if isinstance(tips, list):
                                for tip in tips:
                                    if tip.strip():
                                        recommendation_response += f"- {tip.strip()}\n"
                            else:
                                # Handle string format (legacy data)
                                tip_list = tips.split(". ")
                                for tip in tip_list:
                                    if tip.strip():
                                        recommendation_response += f"- {tip.strip()}.\n"
                            recommendation_response += "\n"

                    # Critical alerts section disabled - no longer prepending alerts to response
                    # if critical_alerts or urgent_recommendations:
                    #     critical_section = "🚨 **CRITICAL HEALTH ALERTS** 🚨\n\n"
                    #     for alert in critical_alerts:
                    #         critical_section += f"{alert}\n\n"

                    #     if urgent_recommendations:
                    #         critical_section += "**IMMEDIATE ACTIONS REQUIRED:**\n"
                    #         for rec in urgent_recommendations:
                    #             critical_section += f"- {rec}\n"
                    #         critical_section += "\n"

                    #     # Prepend critical alerts to the response
                    #     recommendation_response = critical_section + recommendation_response

                    # Add recommendations from kidney function if available
                    if "kidney_function" in user_health_data[user_key]:
                        kidney_data = user_health_data[user_key]["kidney_function"]
                        if "recommendations" in kidney_data["result"] and kidney_data["result"]["recommendations"]:
                            recommendation_response += "**Kidney Function Recommendations:**\n"

                            # Add kidney disease stage if available
                            kidney_stage = kidney_data["result"].get("kidney_stage", "Unknown")
                            if kidney_stage and kidney_stage != "Unknown":
                                recommendation_response += f"Based on your kidney disease stage ({kidney_stage}), here are my recommendations:\n"

                            for rec in kidney_data["result"]["recommendations"]:
                                recommendation_response += f"- {rec}\n"
                            recommendation_response += "\n"

                    # Add recommendations from lipid profile if available
                    if "lipid_profile" in user_health_data[user_key]:
                        lipid_data = user_health_data[user_key]["lipid_profile"]
                        if "recommendations" in lipid_data["result"] and lipid_data["result"]["recommendations"]:
                            recommendation_response += "**Cardiac Health Recommendations:**\n"
                            for rec in lipid_data["result"]["recommendations"][:5]:  # Limit to 5 recommendations
                                recommendation_response += f"- {rec}\n"
                            recommendation_response += "\n"

                            # Add diet plan information if available
                            if "diet_plan" in lipid_data["result"] and lipid_data["result"]["diet_plan"]:
                                diet_plan = lipid_data["result"]["diet_plan"]
                                recommendation_response += "**Heart-Healthy Diet Recommendations:**\n"
                                for food in diet_plan.get("recommended_foods", [])[:3]:  # Limit to 3 foods
                                    recommendation_response += f"- {food}\n"
                                recommendation_response += "\n"

                    # Add recommendations from liver function if available
                    if "liver_function" in user_health_data[user_key]:
                        liver_data = user_health_data[user_key]["liver_function"]
                        if "recommendations" in liver_data["result"] and liver_data["result"]["recommendations"]:
                            recommendation_response += "**Liver Health Recommendations:**\n"
                            for rec in liver_data["result"]["recommendations"][:5]:  # Limit to 5 recommendations
                                recommendation_response += f"- {rec}\n"
                            recommendation_response += "\n"

                    # Add alerts from vital signs if available
                    if "vital_signs" in user_health_data[user_key]:
                        vital_data = user_health_data[user_key]["vital_signs"]
                        if "alerts" in vital_data["result"] and vital_data["result"]["alerts"] and vital_data["result"]["alerts"] != "No abnormal patterns detected.":
                            recommendation_response += "**Vital Signs Recommendations:**\n"
                            alerts = vital_data["result"]["alerts"].split("\n")
                            for alert in alerts:
                                if alert.strip():
                                    recommendation_response += f"- {alert.strip()}\n"
                            recommendation_response += "\n"

                    # Add recommendations from reproductive health if available
                    if "reproductive_health_cycle" in user_health_data[user_key]:
                        cycle_data = user_health_data[user_key]["reproductive_health_cycle"]
                        if "recommendations" in cycle_data["result"] and cycle_data["result"]["recommendations"]:
                            recommendation_response += "**Reproductive Health Recommendations:**\n"
                            for rec in cycle_data["result"]["recommendations"][:5]:  # Limit to 5 recommendations
                                recommendation_response += f"- {rec}\n"
                            recommendation_response += "\n"

                    # Add recommendations from pregnancy monitoring if available
                    if "pregnancy_monitoring" in user_health_data[user_key]:
                        pregnancy_data = user_health_data[user_key]["pregnancy_monitoring"]
                        if "recommendations" in pregnancy_data["result"] and pregnancy_data["result"]["recommendations"]:
                            recommendation_response += "**Pregnancy Care Recommendations:**\n"
                            for rec in pregnancy_data["result"]["recommendations"][:5]:  # Limit to 5 recommendations
                                recommendation_response += f"- {rec}\n"
                            recommendation_response += "\n"

                    # Add recommendations from postpartum health if available
                    if "postpartum_health" in user_health_data[user_key]:
                        postpartum_data = user_health_data[user_key]["postpartum_health"]
                        if "recommendations" in postpartum_data["result"] and postpartum_data["result"]["recommendations"]:
                            recommendation_response += "**Postpartum Recovery Recommendations:**\n"
                            for rec in postpartum_data["result"]["recommendations"][:5]:  # Limit to 5 recommendations
                                recommendation_response += f"- {rec}\n"
                            recommendation_response += "\n"

                    # Add priority recommendations based on health score (prefer real-time over regular)
                    score_to_use = 0
                    score_type = ""

                    if "realtime_health_score" in user_health_data[user_key]:
                        score_data = user_health_data[user_key]["realtime_health_score"]
                        score_to_use = score_data["result"].get("Total Score", 0)
                        score_type = "real-time health"
                    elif "health_score" in user_health_data[user_key]:
                        score_data = user_health_data[user_key]["health_score"]
                        score_to_use = score_data["result"].get("Total Score", 0)
                        score_type = "health"

                    # Add structured recommendations (critical alerts disabled)
                    # Always show structured recommendations since critical alerts are disabled
                    if score_to_use > 0:
                        # Add a personalized priority recommendation
                        recommendation_response += "**Priority Recommendation:**\n"
                        if score_to_use < 50:
                            recommendation_response += f"- 🚨 Your {score_type} score ({score_to_use}) indicates several areas needing attention. I recommend scheduling a comprehensive check-up with your healthcare provider soon.\n\n"
                        elif score_to_use < 70:
                            recommendation_response += f"- ⚠️ Focus on the specific areas mentioned above to improve your overall {score_type} score ({score_to_use}). Small consistent changes can make a big difference.\n\n"
                        else:
                            recommendation_response += f"- ✅ You're doing well with a {score_type} score of {score_to_use}! Continue your healthy habits and consider the recommendations above for further improvement.\n\n"

                    # Add device recommendations
                    recommendation_response += "🛒 **For health monitoring devices, visit [TurboMedics](https://www.turbomedics.com/products) for a great selection of health tracking devices!**"

                    # Use the structured recommendations (critical alerts disabled)
                    model_response = recommendation_response
                    tools_used.append("personalized_recommendations")
                else:
                    # If no health data, prompt user to enter some
                    tool_response += "\n\nI don't have any health data for you yet. Would you like to enter your health data for personalized recommendations? You can choose from:\n\n- Health Score Analysis\n- Vital Signs Monitoring\n- Kidney Function Test\n- Lipid Profile Test\n- Liver Function Test\n\nType the name of the test you'd like to perform."
                    tools_used.append("no_health_data")

            # Health score analysis intent
            elif primary_intent == "health_score":
                # Check if user already has health score data
                if user_key in user_health_data and "health_score" in user_health_data[user_key]:
                    # User already has health score data, don't prompt for another test
                    score_data = user_health_data[user_key]["health_score"]
                    tool_response += f"\n\nI can see you already have a health score of {score_data['result'].get('Total Score', 'Unknown')} with {score_data['result'].get('Health Status', 'Unknown')} status. I'll use this data to provide personalized recommendations."
                    tools_used.append("existing_health_score")
                else:
                    # User doesn't have health score data, prompt for test
                    tool_response += "\n\nWould you like to analyze your health score? Type 'yes' to begin."
                    tools_used.append("health_score_intent")

            # Vital signs monitoring intent
            elif primary_intent == "vital_signs":
                # Check if user already has vital signs data
                if user_key in user_health_data and "vital_signs" in user_health_data[user_key]:
                    # User already has vital signs data, don't prompt for another test
                    tool_response += "\n\nI can see you already have vital signs data on file. I'll use this information to provide personalized health insights."
                    tools_used.append("existing_vital_signs")
                else:
                    # User doesn't have vital signs data, prompt for test
                    tool_response += "\n\nWould you like to enter your vital signs for monitoring? Type 'yes' to begin."
                    tools_used.append("vital_signs_intent")

            # Kidney function test intent
            elif primary_intent == "kidney_function":
                # Check if user already has kidney function data
                if user_key in user_health_data and "kidney_function" in user_health_data[user_key]:
                    # User already has kidney function data, don't prompt for another test
                    tool_response += "\n\nI can see you already have kidney function test results on file. I'll use this data to provide personalized kidney health insights."
                    tools_used.append("existing_kidney_function")
                else:
                    # User doesn't have kidney function data, prompt for test
                    tool_response += "\n\nWould you like to analyze your kidney function? Type 'yes' to begin."
                    tools_used.append("kidney_function_intent")

            # Lipid profile test intent
            elif primary_intent == "lipid_profile":
                # Check if user already has lipid profile data
                if user_key in user_health_data and "lipid_profile" in user_health_data[user_key]:
                    # User already has lipid profile data, don't prompt for another test
                    tool_response += "\n\nI can see you already have lipid profile results on file. I'll use this data to provide personalized cardiac health recommendations."
                    tools_used.append("existing_lipid_profile")
                else:
                    # User doesn't have lipid profile data, prompt for test
                    tool_response += "\n\nWould you like to analyze your lipid profile for cardiac health planning? This will include ASCVD risk assessment, personalized diet recommendations, and a comprehensive cardiac health plan. Type 'yes' to begin."
                    tools_used.append("lipid_profile_intent")

            # Liver function test intent
            elif primary_intent == "liver_function":
                # Check if user already has liver function data
                if user_key in user_health_data and any("liver" in str(data).lower() for data in user_health_data[user_key].values()):
                    # User already has liver-related data, don't prompt for another test
                    tool_response += "\n\nI can see you already have liver function data on file. I'll use this information to provide personalized liver health insights."
                    tools_used.append("existing_liver_function")
                else:
                    # User doesn't have liver function data, prompt for test
                    tool_response += "\n\nWould you like to analyze your liver function? This will include liver enzyme analysis, hepatic health assessment, and personalized recommendations. Type 'yes' to begin."
                    tools_used.append("liver_function_intent")

            # Health consultation intent
            elif primary_intent == "health_consultation":
                # Check if user has any health data for a comprehensive consultation
                if user_key in user_health_data and user_health_data[user_key]:
                    tool_response += "\n\nI'd be happy to provide a comprehensive health consultation based on your data. This will include an analysis of your health metrics, personalized recommendations, specialist referrals if needed, recommended appointments, tests, follow-up schedules, and proactive care coordination. Would you like me to do that now? Just type 'yes' and I'll get started."
                    tool_response += "\n\nIf you'd prefer to book an appointment with a healthcare professional right away, you can **[click here to book an appointment](https://app.turbomedics.com/patient/appointment)**.\n\nTurboMedics offers comprehensive healthcare services with both virtual and in-person options. Once you get to the appointment page, click 'Book Now' to choose:\n• **Online Appointment** - High-quality virtual consultation with secure video technology\n• **Physical Appointment** - In-person visit at our modern healthcare facilities"
                    tools_used.append("consultation_intent")
                else:
                    tool_response += "\n\nI don't have any of your health information yet. I'd need some basic health data to give you a proper consultation. Would you like to enter some health information now? You could start with:\n\n• A quick Health Score Analysis\n• Your Vital Signs\n• Kidney Function Test results\n• Lipid Profile Test results\n• Liver Function Test results\n\nJust let me know which one you'd like to start with."
                    tool_response += "\n\nAlternatively, if you'd like to book an appointment with a healthcare professional directly, you can **[click here to book an appointment](https://app.turbomedics.com/patient/appointment)**.\n\nTurboMedics provides professional healthcare services through our integrated platform. Click 'Book Now' on the appointment page to select:\n• **Online Appointment** - Convenient virtual consultation with professional medical staff\n• **Physical Appointment** - Comprehensive in-person care at our equipped medical centers"
                    tools_used.append("no_health_data")


            # Health trends intent (new)
            elif primary_intent == "health_trends":
                # Handle health trends analysis
                trend_analysis = analyze_health_trends(user_key)
                if trend_analysis:
                    model_response = trend_analysis
                    tools_used.append("health_trends")
                else:
                    tool_response += "\n\nI don't have enough historical health data to analyze trends yet. Would you like to enter your current health data to start tracking?"
                    tools_used.append("no_trend_data")

            # Device recommendations intent
            elif primary_intent == "device_recommendations":
                # Handle device recommendations directly with simple TurboMedics redirect
                device_response = handle_device_recommendations_intent(user_key, query_lower)
                model_response = device_response
                tools_used.append("device_recommendations_processed")

            # Chronic tracker intent
            elif primary_intent == "chronic_tracker":
                # Ask which chronic condition they want to track
                tool_response += "\n\nI'd be happy to help you track your chronic condition. Would you like to track diabetes, hypertension, asthma, heart disease, or kidney disease? Type 'yes' to begin."
                tools_used.append("chronic_tracker_intent")

            # Reproductive health intent
            elif primary_intent == "reproductive_health":
                # Check if user has reproductive health data
                reproductive_data = user_health_data.get(user_key, {}).get("reproductive_health_cycle")
                if reproductive_data:
                    latest_data = reproductive_data.get("result", {})
                    tool_response += f"\n\n**🩸 Reproductive Health Summary**\n\n"
                    if latest_data.get("next_prediction"):
                        pred = latest_data["next_prediction"]
                        tool_response += f"Next period predicted: {pred.get('Next Period Start', 'N/A')}\n"
                        tool_response += f"Ovulation window: {pred.get('Ovulation Window', 'N/A')}\n\n"
                    if latest_data.get("recommendations"):
                        tool_response += "**Recent Recommendations:**\n- " + "\n- ".join(latest_data["recommendations"][:3])
                else:
                    tool_response += "\n\n🩸 **Reproductive Health Tracking**\n\nI can help you track your menstrual cycle, predict your next period, and provide personalized reproductive health insights. To get started, I'll need some basic cycle information.\n\n📱 Use the `/reproductive-health` endpoint with mode='cycle' to log your cycle data, or tell me about your last period and I can guide you through the process."
                tools_used.append("reproductive_health_processed")

            # Pregnancy monitoring intent
            elif primary_intent == "pregnancy_monitoring":
                # Check if user has pregnancy data
                pregnancy_data = user_health_data.get(user_key, {}).get("pregnancy_monitoring")
                if pregnancy_data:
                    latest_data = pregnancy_data.get("result", {})
                    tool_response += f"\n\n**🤰 Pregnancy Monitoring Summary**\n\n"
                    tool_response += f"Gestational Age: {latest_data.get('Gestational Age', 'N/A')}\n"
                    if latest_data.get("Expected Delivery Window"):
                        edd = latest_data["Expected Delivery Window"]
                        tool_response += f"Expected Delivery: {edd.get('Start', 'N/A')} to {edd.get('End', 'N/A')}\n\n"
                    if latest_data.get("recommendations"):
                        tool_response += "**Recent Recommendations:**\n- " + "\n- ".join(latest_data["recommendations"][:3])
                else:
                    tool_response += "\n\n🤰 **Pregnancy Monitoring**\n\nI can help you monitor your pregnancy progress, analyze symptoms, and provide trimester-specific guidance. To get started, I'll need your last menstrual period (LMP) date and any current symptoms.\n\n📱 Use the `/pregnancy-monitoring` endpoint to log your pregnancy data, or tell me your LMP date and I can help track your pregnancy journey."
                tools_used.append("pregnancy_monitoring_processed")

            # Postpartum health intent
            elif primary_intent == "postpartum_health":
                # Check if user has postpartum data
                postpartum_data = user_health_data.get(user_key, {}).get("postpartum_health")
                if postpartum_data:
                    latest_data = postpartum_data.get("result", {})
                    tool_response += f"\n\n**🤱 Postpartum Health Summary**\n\n"
                    tool_response += f"Days since delivery: {latest_data.get('Days Since Delivery', 'N/A')}\n"
                    if latest_data.get("Flags"):
                        tool_response += f"Health flags: {len(latest_data['Flags'])} items to monitor\n\n"
                    if latest_data.get("recommendations"):
                        tool_response += "**Recent Recommendations:**\n- " + "\n- ".join(latest_data["recommendations"][:3])
                else:
                    tool_response += "\n\n🤱 **Postpartum Health Tracking**\n\nI can help you monitor your postpartum recovery, track both mother and baby health, and provide personalized recovery guidance. I'll monitor for potential complications and provide evidence-based recommendations.\n\n📱 Use the `/postpartum-health` endpoint to log your recovery data, or tell me about your delivery date and current concerns, and I can guide you through the tracking process."
                tools_used.append("postpartum_health_processed")

            # Appointment booking intent
            elif primary_intent == "appointment_booking":
                tool_response = "I can help you book an appointment with a healthcare professional. **[Click here to book your appointment](https://app.turbomedics.com/patient/appointment)**\n\nTurboMedics is a comprehensive healthcare platform that connects you with qualified medical professionals. Our platform offers:\n\n🏥 **Comprehensive Care Network** - Access to specialists, general practitioners, and healthcare experts\n💻 **Advanced Telemedicine** - State-of-the-art virtual consultation technology\n🏢 **Modern Facilities** - Well-equipped medical centers for in-person visits\n📊 **Integrated Health Records** - Your health data seamlessly shared with your healthcare provider\n\nOnce you get to the TurboMedics appointment page, click on the 'Book Now' button. You'll then see two different types of appointments available:\n\n• **Online Appointment** - Virtual consultation via video call with HD quality and secure connection\n• **Physical Appointment** - In-person visit at one of our modern healthcare facilities\n\nChoose the option that best suits your needs and preferences. Both options provide the same high-quality care with access to your complete health profile."
                # Override the model response completely - don't add to existing response
                model_response = tool_response
                tool_response = ""  # Clear tool_response to prevent duplication
                tools_used.append("appointment_booking_intent")

        # Add tool response to model response if any
        if tool_response:
            model_response += tool_response

        # Add proactive device recommendations for general health queries
        if not tool_response and user_key in user_health_data and user_health_data[user_key]:
            # Check if the query is health-related and user has health data
            health_related_keywords = [
                "health", "advice", "recommendation", "improve", "better", "monitor", "track",
                "vitals", "wellness", "fitness", "medical", "doctor", "treatment", "care",
                "symptoms", "condition", "disease", "prevention", "lifestyle", "diet", "exercise",
                "home monitoring", "self monitoring", "tracking", "measurement", "check"
            ]

            # Also check for specific health concerns that might benefit from device monitoring
            device_trigger_keywords = [
                "blood pressure monitor", "heart rate monitor", "glucose meter", "diabetes monitor",
                "blood pressure device", "weight scale", "thermometer", "pulse oximeter",
                "monitoring device", "health device", "medical device"
            ]

            # Don't trigger device recommendations for exercise/diet queries
            exercise_diet_keywords = [
                "exercise", "workout", "fitness", "training", "diet", "meal", "food", "nutrition",
                "physical activity", "cardio", "strength", "yoga", "running", "walking"
            ]

            # Only trigger device recommendations for specific device queries, not general health queries
            if (any(keyword in query_lower for keyword in device_trigger_keywords) and
                not any(keyword in query_lower for keyword in exercise_diet_keywords)):

                # Check if we haven't already recommended devices recently
                recent_device_rec = False
                if len(chat_histories[user_key]) > 2:
                    for message in chat_histories[user_key][-3:]:
                        if message["role"] == "assistant" and "turbomedics" in message["content"].lower():
                            recent_device_rec = True
                            break

                if not recent_device_rec:
                    # Add simple device recommendation suggestion to the response
                    model_response += "\n\n🛒 **For health monitoring devices, check out [TurboMedics](https://www.turbomedics.com/products) - they have a great selection of health tracking devices!**"

        # Add response to chat history
        chat_histories[user_key].append({"role": "assistant", "content": model_response})

        # Check if health consultation intent was detected
        response_data = {
            "response": model_response,
            "chat_title": chat_titles[user_key],
            "chat_history": chat_histories[user_key],
            "tools_used": tools_used
        }

        # Add quick action for health consultation if that intent was detected
        if "consultation_intent" in tools_used:
            response_data["show_health_consultation"] = True
            response_data["health_consultation_link"] = f"/health-consultation?user_id={user_key[0]}_{user_key[1]}"

        return response_data
    except Exception as e:
        error_msg = f"Failed to process agent query: {str(e)}"
        logger.error(error_msg)
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise Exception(error_msg)

# === ENDPOINTS ===
@app.get("/")
async def root():
    return {"message": "Integrated Health Agent API is running"}

@app.get("/health")
async def health_check():
    """Simple health check endpoint for monitoring"""
    return {"status": "healthy"}

@app.get("/default-health-data")
async def default_health_data():
    """Get default health data"""
    try:
        # Use the exact DEFAULT_HEALTH_DATA structure as specified
        default_data = {
            "Glucose": None,
            "Hepatitis B": "Unknown",
            "Systolic": None,
            "Diastolic": None,
            "Waist Circumference": None,
            "Hiv": "Unknown",
            "Fev": None,
            "Temperature": None,
            "Ecg": None,
            "Spo2": None,
            "Weight": None,
            "Widal Test": {
                "Typhi O": "Unknown",
                "Typhi H": "Unknown",
                "Paratyphi AH": "Unknown",
                "Paratyphi BH": "Unknown"
            },
            "Malaria": "Unknown",
            "Kidney": None,
            "Lipid": None,
            "Liver": None,
            "Respiratory": None
        }

        logging.info(f"Returning default health data: {json.dumps(default_data)}")
        return default_data
    except Exception as e:
        error_msg = f"Error getting default health data: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

@app.get("/user-health-data/{user_id}")
async def get_user_health_data(user_id: str, data_type: Optional[str] = None):
    """Get stored health data for a specific user

    Args:
        user_id: The ID of the user
        data_type: Optional type of health data to retrieve (vital_signs, health_score, kidney_function, lipid_profile)
                  If not provided, returns all health data for the user
    """
    try:
        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        if user_key not in user_health_data:
            return {"message": "No health data found for this user"}

        if data_type:
            if data_type not in user_health_data[user_key]:
                return {"message": f"No {data_type} data found for this user"}
            return user_health_data[user_key][data_type]

        return user_health_data[user_key]
    except Exception as e:
        error_msg = f"Error retrieving user health data: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

@app.get("/status")
async def status():
    """Check if the server is running and models are loaded"""
    models_status = {}
    for model_name in [QWEN_MODEL, DEEPSEEK_MODEL]:
        models_status[model_name] = {
            "vector_index": model_name in vector_indexes,
            "metadata": model_name in vector_docs,
            "embeddings": model_name in embedding_models
        }

    return {
        "status": "running",
        "models": models_status
    }

# This endpoint is already defined above, so we're removing the duplicate

from fastapi import Query

@app.get("/chat-history")
async def get_chat_history(user_id: str = Query(...)):
    """Return chat history and health data for the user"""
    try:
        # Convert user_id to proper key format for consistency
        user_key = convert_user_id_to_key(user_id)

        # Get messages for this user (using tuple key)
        messages = chat_histories.get(user_key, [])

        # Get health data (vital signs, health score, etc.) using tuple key
        health_data = user_health_data.get(user_key, {})

        # Get chat title if available (using tuple key)
        title = chat_titles.get(user_key, "Health Chat")

        return {
            "messages": messages,
            "health_data": health_data,
            "chat_title": title
        }
    except Exception as e:
        error_msg = f"Error fetching chat history: {str(e)}"
        logging.error(error_msg)
        return {"error": error_msg}


def count_words(text: str) -> int:
    """Count words in a text string"""
    return len(text.split())

def summarize_chat_history(messages: list, max_messages: int = 200) -> list:
    """
    Summarize chat history to reduce token count while preserving context
    Keep the most recent messages and summarize older ones
    """
    if len(messages) <= max_messages:
        return messages

    # Keep the most recent messages (last 150)
    recent_messages = messages[-(max_messages - 50):]

    # Summarize older messages (everything before the recent ones)
    older_messages = messages[:-(max_messages - 50)]

    if older_messages:
        # Create a summary of older conversation
        summary_content = "Previous conversation summary:\n"

        # Count health-related interactions
        health_topics = []
        for msg in older_messages:
            content = msg.get('content', '').lower()
            if any(topic in content for topic in ['health score', 'kidney', 'lipid', 'lung', 'blood pressure', 'glucose', 'device']):
                if msg.get('role') == 'user':
                    health_topics.append(f"User asked about: {content[:50]}...")
                elif msg.get('role') == 'assistant' and len(content) > 100:
                    health_topics.append(f"Assistant provided health analysis")

        if health_topics:
            summary_content += f"- {len(health_topics)} health-related interactions\n"
            summary_content += f"- Topics discussed: {', '.join(set([topic.split(':')[0] for topic in health_topics[:5]]))}\n"

        summary_content += f"- Total messages summarized: {len(older_messages)}\n"
        summary_content += "Recent conversation continues below..."

        # Preserve the original system message (which contains health data context)
        original_system_message = messages[0] if messages and messages[0].get("role") == "system" else None

        # Create summary message
        summary_message = {
            "role": "system",
            "content": summary_content
        }

        # If we have an original system message with health data, combine it with the summary
        if original_system_message:
            combined_system_content = original_system_message["content"] + "\n\n" + summary_content
            combined_system_message = {
                "role": "system",
                "content": combined_system_content
            }
            # Return combined system message + recent messages (excluding the original system message from recent_messages)
            recent_messages_without_system = [msg for msg in recent_messages if msg.get("role") != "system"]
            return [combined_system_message] + recent_messages_without_system
        else:
            # Return summary + recent messages
            return [summary_message] + recent_messages

    return recent_messages

@app.post("/query")
async def get_response(chat: ChatRequest):
    """Handle chat queries through the agent"""
    try:
        user_id, session_id, query, model = chat.user_id, chat.session_id, chat.query, chat.model

        # Validate query word count (max 300 words)
        word_count = count_words(query)
        if word_count > 300:
            return {
                "error": f"Query too long ({word_count} words). Please limit your message to 300 words or less.",
                "word_count": word_count,
                "max_words": 300,
                "suggestion": "Try breaking your question into smaller parts or focus on the most important aspects."
            }

        # Validate model selection
        if model not in [QWEN_MODEL, DEEPSEEK_MODEL]:
            return {"error": f"Invalid model selection. Choose either {QWEN_MODEL} or {DEEPSEEK_MODEL}."}

        # Convert user_id to proper key format to ensure health data access
        user_key = convert_user_id_to_key(user_id)
        patient_id, session_id_from_key = user_key

        # Use the session_id from the converted key to ensure consistency with stored health data
        # This ensures we access the same user_key tuple that was used to store the health data
        result = process_agent_query(query, patient_id, session_id_from_key, model)

        return result
    except Exception as e:
        error_msg = f"Error processing query: {str(e)}"
        logger.error(error_msg)
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

@app.post("/track-progress")
async def track_vital_progress(request: ProgressTrackRequest):
    try:
        user_id = request.user_id
        data = request.vital_signs
        timestamp = datetime.now().isoformat()

        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        if user_key not in user_health_data:
            user_health_data[user_key] = []

        user_health_data[user_key].append({**data, "timestamp": timestamp})
        logging.info(f"Updated progress history for user: {user_id}")

        summary = generate_monthly_summary(user_key, user_health_data)
        recommendations = generate_trend_recommendations(summary.get("trend_analysis", {}))

        return {
            "active_vitals": data,
            "monthly_summary": summary,
            "recommendations": recommendations,
            "raw_data_points": len(user_health_data[user_key])
        }

    except Exception as e:
        logging.error(f"Error tracking vitals for {request.user_id}: {str(e)}")
        return {"error": str(e)}

@app.post("/vital-signs")
async def process_vital_signs_endpoint(request: VitalSignsRequest):
    """Process vital signs data"""
    try:
        user_id = request.user_id
        vital_signs = request.vital_signs

        # Process the vital signs
        result = process_vital_signs(vital_signs)

        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        # Save vital signs data to user health data store
        if user_key not in user_health_data:
            user_health_data[user_key] = {}
        user_health_data[user_key]["vital_signs"] = {
            "data": vital_signs,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        logging.info(f"Saved vital signs data for user {user_id}")

        # Update chat history if user exists
        if user_id in chat_histories:
            vital_signs_str = ", ".join([f"{k}: {v}" for k, v in vital_signs.items()])
            chat_histories[user_id].append({"role": "user", "content": f"My vital signs are: {vital_signs_str}"})

            response_content = f"I've analyzed your vital signs.\n\n{result.get('analysis', '')}"
            if result.get('alerts'):
                response_content += f"\n\nAlerts: {result['alerts']}"

            chat_histories[user_id].append({"role": "assistant", "content": response_content})

        return result
    except Exception as e:
        error_msg = f"Error processing vital signs: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

# Health score endpoint removed - using realtime-health-score instead

@app.post("/kidney-function")
async def analyze_kidney_function_endpoint(request: KidneyFunctionRequest):
    """Analyze kidney function data using the updated tool"""
    try:
        user_id = request.user_id
        kidney_data = request.kidney_data

        if not kidney_data:
            error_msg = "No kidney data provided"
            logging.error(error_msg)
            return {"error": error_msg}

        # Run analysis tool - the updated tool returns a comprehensive result
        result = kidney_function_analysis_tool(kidney_data)

        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        # Save kidney data to user health data store
        if user_key not in user_health_data:
            user_health_data[user_key] = {}
        user_health_data[user_key]["kidney_function"] = {
            "data": kidney_data,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        logging.info(f"Saved kidney function data for user {user_key}")

        # Format for chat memory (optional)
        if user_key in chat_histories:
            chat_histories[user_key].append({"role": "user", "content": "Please analyze my kidney function."})

            # Use the doctor-like summary if available, otherwise format a standard summary
            if "doctor_summary" in result and result["doctor_summary"]:
                doctor_summary = result.get("doctor_summary", "")
                overall_health = result.get('overall_health', 'Unknown')
                kidney_stage = result.get('kidney_stage', 'Unknown')

                summary = f"**Kidney Function Analysis**\n\n"
                summary += f"{doctor_summary}\n\n"

                # Add kidney disease stage if available
                if kidney_stage and kidney_stage != "Unknown":
                    summary += f"**Kidney Disease Stage:** {kidney_stage}\n\n"

                summary += f"**Detailed findings:**\n{overall_health}\n"

                # Add analysis results
                analysis_items = result.get("analysis", [])
                if analysis_items:
                    summary += "\n**Parameter Analysis:**\n"
                    # Only include the most important parameters in the chat summary
                    key_params = ["BUN", "Serum Creatinine", "eGFR", "ACR"]
                    for item in analysis_items:
                        for param in key_params:
                            if item.startswith(f"{param}:") or item.startswith(f"{param} Detail:"):
                                summary += f"- {item}\n"
                    summary += "\n"

                # Add recommendations if available
                if result.get("recommendations"):
                    summary += "\n**Renal Health Management Recommendations:**\n"
                    for rec in result["recommendations"]:
                        summary += f"- {rec}\n"
            else:
                # Fallback to the original formatting
                analysis_items = result.get("analysis", [])
                formatted_analysis = "\n".join([f"- {item}" for item in analysis_items]) if isinstance(analysis_items, list) else analysis_items

                summary = f"🧪 Kidney Function Analysis\n\n"
                if formatted_analysis:
                    summary += f"{formatted_analysis}\n\n"

                summary += f"🩺 Overall Health: {result.get('overall_health', 'Unknown')}\n"

                # Add kidney disease stage if available
                kidney_stage = result.get('kidney_stage', 'Unknown')
                if kidney_stage and kidney_stage != "Unknown":
                    summary += f"🏥 Kidney Disease Stage: {kidney_stage}\n"

                summary += f"📊 Confidence Level: {result.get('confidence_level', 'Unknown')}"

                if result.get("missing_parameters"):
                    summary += f"\n🔍 Missing Parameters: {', '.join(result['missing_parameters'])}"

                # Add recommendations if available
                if result.get("recommendations"):
                    summary += "\n\n🔸 Renal Health Management Recommendations:\n"
                    for rec in result["recommendations"]:
                        summary += f"- {rec}\n"

            # Add device recommendation prompt (following agent_app pattern)
            device_prompt = result.get("device_recommendation_prompt", "")
            if device_prompt:
                summary += device_prompt

            chat_histories[user_key].append({"role": "assistant", "content": summary})

        return result

    except Exception as e:
        error_msg = f"Error analyzing kidney function: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

@app.post("/health-consultation")
async def health_consultation_endpoint_post(request: UserIDRequest, treat_unknown_as_null: bool = Query(False)):
    """Perform a comprehensive health consultation using all available health data (POST method)"""
    return await health_consultation_endpoint(request.user_id, treat_unknown_as_null)

@app.get("/health-consultation")
async def health_consultation_endpoint_get(user_id: str = Query(...), treat_unknown_as_null: bool = Query(False)):
    """Perform a comprehensive health consultation using all available health data (GET method)"""
    return await health_consultation_endpoint(user_id, treat_unknown_as_null)

async def health_consultation_endpoint(user_id: str, treat_unknown_as_null: bool = False):
    """Perform a comprehensive health consultation using all available health data"""
    try:
        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        # Check if user has health data
        if user_key not in user_health_data or not user_health_data[user_key]:
            return {
                "error": "No health data available for this user",
                "message": "Please complete at least one health assessment before requesting a consultation."
            }

        # Process health data to handle "Unknown" values if requested
        processed_health_data = {}
        if treat_unknown_as_null:
            for category, data in user_health_data[user_key].items():
                processed_health_data[category] = data.copy()

                # Process data fields if they exist
                if "data" in data and isinstance(data["data"], dict):
                    processed_data = {}
                    for key, value in data["data"].items():
                        if value != "Unknown" and value is not None and value != "" and value != "null":
                            processed_data[key] = value
                    processed_health_data[category]["data"] = processed_data

                # Process result fields if they exist
                if "result" in data and isinstance(data["result"], dict):
                    for key, value in data["result"].items():
                        if value == "Unknown":
                            data["result"][key] = None
        else:
            processed_health_data = user_health_data[user_key]

        # Prepare input for the consultation tool
        consultation_input = {
            "user_id": user_id,
            "health_data": processed_health_data
        }

        # Run the comprehensive health consultation tool
        consultation_result = json.loads(automated_health_consultation(json.dumps(consultation_input)))

        # Save consultation result to user health data
        user_health_data[user_key]["health_consultation"] = {
            "result": consultation_result,
            "timestamp": datetime.now().isoformat()
        }

        # Format the consultation result in a more conversational, human-like way
        urgency = consultation_result.get("urgency_level", "Low")
        urgency_emoji = "🟢" if urgency == "Low" else "🟡" if urgency == "Medium" else "🔴" if urgency == "High" else "⚠️"

        # Start with a friendly greeting
        formatted_result = "Hi there! I've looked through your health information and here's what I found.\n\n"

        # Add urgency level with appropriate wording
        if urgency == "Low":
            formatted_result += f"{urgency_emoji} Overall, your health indicators look good! There's no immediate cause for concern based on the data I can see.\n\n"
        elif urgency == "Medium":
            formatted_result += f"{urgency_emoji} I've noticed a few things that might need some attention. Nothing urgent, but worth discussing with your doctor in the next few weeks.\n\n"
        else:
            formatted_result += f"{urgency_emoji} I've found some concerning indicators that should be addressed soon. I'd recommend speaking with a healthcare provider as soon as possible.\n\n"

        # Add medical advice in a conversational way
        medical_advice = consultation_result.get("medical_advice", [])
        if medical_advice:
            formatted_result += "Here's my advice based on your health data:\n\n"
            for advice in medical_advice:
                # Remove bullet points and make more conversational
                advice_text = advice
                if advice.startswith("- "):
                    advice_text = advice[2:]
                formatted_result += f"{advice_text}\n\n"

        # Add specialist recommendations if any
        specialist_recommendations = consultation_result.get("specialist_recommendations", [])
        if specialist_recommendations:
            if len(specialist_recommendations) == 1:
                formatted_result += f"Based on what I'm seeing, you might benefit from talking to a {specialist_recommendations[0]}.\n\n"
            else:
                formatted_result += "Based on your health data, you might benefit from consulting with these specialists:\n\n"
                for specialist in specialist_recommendations:
                    formatted_result += f"• A {specialist}\n"
                formatted_result += "\n"

        # Add appointment recommendations from appointment intelligence
        appointment_recommendations = consultation_result.get("appointment_recommendations", [])
        appointment_reasons = consultation_result.get("appointment_reasons", {})
        appointment_urgency_levels = consultation_result.get("appointment_urgency_levels", {})

        if appointment_recommendations:
            formatted_result += "**Recommended Appointments:**\n\n"
            for appointment in appointment_recommendations:
                reason = appointment_reasons.get(appointment, "General health monitoring")
                urgency = appointment_urgency_levels.get(appointment, "Low")
                urgency_icon = "🔴" if urgency == "High" else "🟡" if urgency == "Medium" else "🟢"

                formatted_result += f"{urgency_icon} **{appointment}**: {reason}\n"
            formatted_result += "\n"

        # Add recommended tests from appointment intelligence
        recommended_tests = consultation_result.get("recommended_tests", [])
        if recommended_tests:
            formatted_result += "**Recommended Tests and Screenings:**\n\n"
            for test in recommended_tests:
                formatted_result += f"• {test}\n"
            formatted_result += "\n"

        # Add follow-up schedule from appointment intelligence
        follow_up_schedule = consultation_result.get("follow_up_schedule", {})
        if follow_up_schedule:
            formatted_result += "**Follow-up Schedule:**\n\n"
            for check, timeframe in follow_up_schedule.items():
                formatted_result += f"• {check}: {timeframe}\n"
            formatted_result += "\n"

        # Add care coordination recommendations from appointment intelligence
        care_coordination = consultation_result.get("care_coordination", [])
        if care_coordination:
            formatted_result += "**Proactive Care Coordination:**\n\n"
            for care in care_coordination:
                formatted_result += f"• {care}\n"
            formatted_result += "\n"

        # Add integrated analysis in a more conversational way
        integrated_analysis = consultation_result.get("integrated_analysis", [])
        if integrated_analysis:
            formatted_result += "Let me break down what I'm seeing in your health data:\n\n"
            for analysis in integrated_analysis:
                # Make it more conversational
                if analysis.startswith("Health score:"):
                    # Keep the emoji if present
                    emoji_match = re.search(r'(🩸|🍎|🚶‍♀️|[^\w\s])', analysis)
                    emoji = emoji_match.group(0) + " " if emoji_match else ""
                    content = analysis.split(":", 1)[1].strip()
                    formatted_result += f"{emoji}I notice that {content}\n\n"
                else:
                    formatted_result += f"{analysis}\n\n"

        # Add health summary in a more conversational way
        health_summary = consultation_result.get("health_summary", {})
        if health_summary:
            formatted_result += "Here's a quick summary of your health metrics:\n\n"

            if "health_score" in health_summary:
                score_data = health_summary["health_score"]
                score = score_data.get('score', 'N/A')
                status = score_data.get('status', 'Unknown')
                if status != "Unknown":
                    formatted_result += f"Your overall health score is {score}, which is considered '{status}'. "
                else:
                    formatted_result += f"Your overall health score could not be determined due to insufficient data. "

            if "kidney_function" in health_summary:
                kidney_data = health_summary["kidney_function"]
                kidney_health = kidney_data.get('overall_health', 'Unknown')
                kidney_stage = kidney_data.get('kidney_stage', 'Unknown')

                if kidney_health != "Unknown":
                    # Remove emoji if present
                    kidney_health = re.sub(r'[^\w\s]', '', kidney_health).strip()
                    formatted_result += f"Your kidney function appears to be {kidney_health.lower()}. "
                    if kidney_stage and kidney_stage != "Unknown":
                        formatted_result += f"You are currently at {kidney_stage}. "
                else:
                    formatted_result += f"Your kidney function could not be determined due to insufficient data. "

            if "lipid_profile" in health_summary:
                lipid_data = health_summary["lipid_profile"]
                cv_risk = lipid_data.get('ascvd_risk', 'Unknown')

                if cv_risk != "Unknown":
                    formatted_result += f"Your cardiovascular risk is in the {cv_risk.lower()} range. "

                    # Add diet plan information if available
                    if "diet_plan" in lipid_data:
                        formatted_result += f"I've included a heart-healthy diet plan in your cardiac health analysis. "

                    # Add cardiac health plan information if available
                    if "cardiac_health_plan" in lipid_data:
                        formatted_result += f"Your cardiac health plan includes specific monitoring recommendations and follow-up guidance."
                else:
                    formatted_result += f"Your cardiovascular risk could not be determined due to insufficient data. "

            formatted_result += "\n\n"

        # Add appointment booking link if needed
        if (consultation_result.get("doctor_visit_recommended", False) or
            consultation_result.get("appointment_recommendations") or
            consultation_result.get("recommended_tests") or
            specialist_recommendations):

            formatted_result += "**Ready to take the next step?**\n\n"
            formatted_result += "I recommend booking an appointment with a healthcare professional for further evaluation and personalized care.\n\n"
            formatted_result += "**[Click here to book your appointment](https://app.turbomedics.com/patient/appointment)**\n\n"
            formatted_result += "TurboMedics is your trusted healthcare partner, offering comprehensive medical services through our network of qualified professionals. Your complete health profile will be securely shared with your chosen healthcare provider for optimal care.\n\n"
            formatted_result += "On the TurboMedics appointment page, click 'Book Now' to choose from:\n"
            formatted_result += "• **Online Appointment** - Professional virtual consultation with secure, high-quality video technology\n"
            formatted_result += "• **Physical Appointment** - Comprehensive in-person care at our modern, well-equipped medical facilities\n\n"
            formatted_result += "Both options provide access to the same high standard of healthcare with your complete health history available to your provider.\n\n"

        # Add to chat history
        if user_key in chat_histories:
            chat_histories[user_key].append({"role": "user", "content": "I'd like a health consultation based on my data."})
            chat_histories[user_key].append({"role": "assistant", "content": formatted_result})

        return {
            "consultation": formatted_result,
            "doctor_visit_recommended": consultation_result.get("doctor_visit_recommended", False),
            "urgency_level": urgency,
            "appointment_recommendations": consultation_result.get("appointment_recommendations", []),
            "recommended_tests": consultation_result.get("recommended_tests", []),
            "follow_up_schedule": consultation_result.get("follow_up_schedule", {}),
            "care_coordination": consultation_result.get("care_coordination", []),
            "appointment_booking_link": consultation_result.get("appointment_booking_link", "https://app.turbomedics.com/patient/appointment"),
            "booking_message": consultation_result.get("booking_message", "Click the link to book an appointment with healthcare professionals.")
        }

    except Exception as e:
        error_msg = f"Error performing health consultation: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

@app.post("/lipid-profile")
async def analyze_lipid_profile_endpoint(request: LipidProfileRequest):
    """Analyze lipid profile data"""
    try:
        user_id = request.user_id
        lipid_data = request.lipid_data

        if not lipid_data:
            return {"error": "No lipid data provided."}

        result = analyze_lipid_profile(lipid_data)

        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        # Save lipid data to user health data store
        if user_key not in user_health_data:
            user_health_data[user_key] = {}
        user_health_data[user_key]["lipid_profile"] = {
            "data": lipid_data,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        logging.info(f"Saved lipid profile data for user {user_id}")

        # Add formatted text for chat history (if integrated)
        if user_id in chat_histories:
            chat_histories[user_id].append({
                "role": "user",
                "content": "Please analyze my lipid profile."
            })

            # Create a comprehensive summary with all the new features
            summary = f"**Cardiac Health Analysis**\n\n"

            # Add doctor's summary if available
            if "doctor_summary" in result and result["doctor_summary"]:
                summary += f"{result['doctor_summary']}\n\n"

            # Add classification results
            summary += "**Here's a breakdown of your results:**\n"
            for component, level in result["classification"].items():
                component_name = component.replace('_', ' ').title()

                # Add emoji indicators
                if component == 'hdl':  # For HDL, high is good
                    emoji = "✅" if level in ['optimal', 'high'] else "⚠️" if level == 'borderline' else "❗"
                else:  # For all other components, low is generally good
                    emoji = "✅" if level == 'optimal' else "⚠️" if level in ['borderline', 'near optimal'] else "❗"

                summary += f"{emoji} **{component_name}**: {level.title()}\n"

            # Add ASCVD risk
            risk = result.get('ascvd_risk', 'Unknown')
            summary += f"\n**Cardiovascular Risk Assessment**: {risk} "
            if "low" in risk.lower():
                summary += "✅"
                risk_explanation = "This means your risk of developing cardiovascular disease in the next 10 years is relatively low based on your lipid values."
            elif "borderline" in risk.lower():
                summary += "⚠️"
                risk_explanation = "This suggests you have some risk factors that could increase your chances of developing cardiovascular disease in the next 10 years."
            elif "intermediate" in risk.lower() or "moderate" in risk.lower():
                summary += "⚠️"
                risk_explanation = "This suggests you have several risk factors that could increase your chances of developing cardiovascular disease in the next 10 years."
            elif "high" in risk.lower():
                summary += "❗"
                risk_explanation = "This indicates a higher probability of developing cardiovascular disease in the next 10 years, and we should take proactive steps to address this."
            else:
                risk_explanation = "Please consult with your healthcare provider to understand your cardiovascular risk better."

            summary += f"\n{risk_explanation}\n"

            # Add detailed parameter explanations
            parameter_explanations = result.get("parameter_explanations", [])
            if parameter_explanations:
                summary += "\n**Understanding Your Lipid Profile:**\n"
                for param in parameter_explanations[:3]:  # Limit to 3 to avoid overly long messages
                    summary += f"- **{param['parameter']}** ({param['value']} mg/dL): {param['explanation']} {param['status_explanation']}\n"

            # Add recommendations
            recommendations = result.get("recommendations", [])
            if recommendations:
                summary += "\n**Here's what I recommend:**\n"
                for rec in recommendations[:5]:  # Limit to 5 recommendations
                    summary += f"- {rec}\n"

            # Add diet plan highlights
            diet_plan = result.get("diet_plan", {})
            if diet_plan:
                summary += f"\n**{diet_plan.get('title', 'Heart-Healthy Diet Plan')}**\n"
                summary += f"{diet_plan.get('overview', '')}\n\n"

                # Add a few recommended foods
                recommended_foods = diet_plan.get('recommended_foods', [])
                if recommended_foods:
                    summary += "**Key Recommended Foods:**\n"
                    for food in recommended_foods[:3]:  # Limit to 3 items
                        summary += f"- {food}\n"

            # Add cardiac health plan highlights
            cardiac_health_plan = result.get("cardiac_health_plan", {})
            if cardiac_health_plan:
                summary += f"\n**{cardiac_health_plan.get('title', 'Cardiac Health Plan')}**\n"
                summary += f"{cardiac_health_plan.get('overview', '')}\n\n"

                if cardiac_health_plan.get('follow_up'):
                    summary += f"**Follow-up Plan:** {cardiac_health_plan.get('follow_up')}\n"

            # Add device recommendation prompt (following agent_app pattern)
            device_prompt = result.get("device_recommendation_prompt", "")
            if device_prompt:
                summary += device_prompt

            chat_histories[user_id].append({
                "role": "assistant",
                "content": summary
            })

        return result
    except Exception as e:
        error_msg = f"Error analyzing lipid profile: {str(e)}"
        logging.error(error_msg)
        return {"error": error_msg}

@app.post("/lung-capacity")
async def analyze_lung_capacity_endpoint(request: LungCapacityRequest):
    """Analyze lung capacity and respiratory health"""
    try:
        user_id = request.user_id
        spirometry_data = request.spirometry_data

        if not spirometry_data:
            return {"error": "No spirometry data provided."}

        result = process_lung_capacity(spirometry_data)

        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        # Save lung capacity data to user health data store
        if user_key not in user_health_data:
            user_health_data[user_key] = {}
        user_health_data[user_key]["lung_capacity"] = {
            "data": spirometry_data,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        logging.info(f"Saved lung capacity data for user {user_id}")

        # Add formatted text for chat history (if integrated)
        if user_id in chat_histories:
            chat_histories[user_id].append({
                "role": "user",
                "content": "Please analyze my lung capacity and respiratory health."
            })

            # Use the doctor-like summary if available, otherwise format a standard summary
            if "doctor_summary" in result and result["doctor_summary"]:
                doctor_summary = result.get("doctor_summary", "")
                recommendations = "\n- ".join(result.get("recommendations", ["No specific recommendations."]))

                summary = f"**Lung Capacity Analysis**\n\n"
                summary += f"{doctor_summary}\n\n"
                summary += f"**Here's what I recommend:**\n- {recommendations}"
            else:
                # Fallback to the original formatting
                analysis_text = "\n".join([f"- {item}" for item in result.get("analysis", [])])
                risk_level = result.get("respiratory_risk_level", "Unknown")
                conditions = ", ".join(result.get("potential_conditions", ["None identified"]))
                recommendations = "\n- ".join(result.get("recommendations", ["No specific recommendations."]))

                summary = f"**Lung Capacity Analysis**\n\n"
                summary += f"**Analysis:**\n{analysis_text}\n\n"
                summary += f"**Respiratory Risk Level:** {risk_level}\n\n"
                summary += f"**Potential Conditions:** {conditions}\n\n"
                summary += f"**Recommendations:**\n- {recommendations}"

            # Add device recommendation prompt (following agent_app pattern)
            device_prompt = result.get("device_recommendation_prompt", "")
            if device_prompt:
                summary += device_prompt

            chat_histories[user_id].append({
                "role": "assistant",
                "content": summary
            })

        return result
    except Exception as e:
        error_msg = f"Error analyzing lung capacity: {str(e)}"
        logging.error(error_msg)
        return {"error": error_msg}


@app.post("/test-results")
async def analyze_test_results_endpoint(request: TestResultsRequest):
    """Analyze malaria and widal test results"""
    try:
        user_id = request.user_id
        test_results = request.test_results

        if not test_results:
            return {"error": "No test results provided."}

        result = process_test_results(test_results)

        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        # Save test results data to user health data store
        if user_key not in user_health_data:
            user_health_data[user_key] = {}
        user_health_data[user_key]["test_results"] = {
            "data": test_results,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        logging.info(f"Saved test results data for user {user_id}")

        # Update chat history if user exists
        if user_id in chat_histories:
            # Create a summary of the test results for the chat history
            test_types = test_results.get("selected_tests", [])
            test_types_str = ", ".join(test_types)

            # Add user message
            chat_histories[user_id].append({
                "role": "user",
                "content": f"I've submitted my {test_types_str} results for analysis."
            })

            # Create a summary of the analysis for the assistant's response
            summary = f"I've analyzed your {test_types_str} results.\n\n"

            # Add doctor summary if available
            if "doctor_summary" in result:
                summary += f"{result['doctor_summary']}\n\n"

            # Add urgency level with appropriate emoji
            urgency_level = result.get("urgency_level", "Low")
            if urgency_level == "High":
                summary += "**Urgency Level: 🚨 High**\n\n"
            elif urgency_level == "Medium":
                summary += "**Urgency Level: ⚠️ Medium**\n\n"
            else:
                summary += "**Urgency Level: ✅ Low**\n\n"

            # Add recommendations
            if "recommendations" in result and result["recommendations"]:
                summary += "**Recommendations:**\n"
                for rec in result["recommendations"]:
                    summary += f"{rec}\n"

            # Add assistant message
            chat_histories[user_id].append({
                "role": "assistant",
                "content": summary
            })

        return result
    except Exception as e:
        error_msg = f"Error analyzing test results: {str(e)}"
        logging.error(error_msg)
        return {"error": error_msg}




@app.post("/realtime-health-score")
async def realtime_health_score_endpoint(request: RealTimeHealthScoreRequest):
    """Generate a real-time health score that combines vitals, lifestyle, and test results"""
    try:
        user_id = request.user_id
        health_data = request.health_data

        if not health_data:
            return {"error": "No health data provided."}

        # Process health data to handle "Unknown" values
        processed_health_data = {}
        for key, value in health_data.items():
            # Skip "Unknown" values for all fields
            if value == "Unknown":
                continue
            # Skip None values
            elif value is None or value == "" or value == "null":
                continue
            # Handle Widal Test with nested antibody results
            elif key == "Widal Test" and isinstance(value, dict):
                # Process nested Widal Test results
                widal_results = {}
                for antibody, result in value.items():
                    if result not in ["Unknown", None, "", "null"]:
                        widal_results[antibody] = result
                if widal_results:  # Only add if we have valid results
                    processed_health_data[key] = widal_results
            # Handle other test results with string values
            elif key in ["Malaria", "Hepatitis B", "Voluntary Serology"]:
                processed_health_data[key] = value
            # Convert numeric values to float
            else:
                try:
                    processed_health_data[key] = float(value)
                except (ValueError, TypeError):
                    # If conversion fails, keep the original value
                    processed_health_data[key] = value

        # Process the real-time health score with processed data
        result = process_realtime_health_score(processed_health_data)

        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        # Save real-time health score data to user health data store
        if user_key not in user_health_data:
            user_health_data[user_key] = {}
        user_health_data[user_key]["realtime_health_score"] = {
            "data": processed_health_data,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        logging.info(f"Saved real-time health score data for user {user_key}")

        # Update chat history if user exists
        if user_key in chat_histories:
            # Add user message
            chat_histories[user_key].append({
                "role": "user",
                "content": "I'd like to see my real-time health score."
            })

            # Create a summary of the analysis for the assistant's response
            summary = "I've analyzed your comprehensive health data and generated a real-time health score.\n\n"

            # Add total score and health status
            summary += f"**Total Health Score: {result.get('Total Score', 0)}** - {result.get('Health Status', 'Unknown')}\n\n"

            # Add category scores
            if "Category Scores" in result:
                summary += "**Category Breakdown:**\n"
                for category, score in result["Category Scores"].items():
                    summary += f"- {category}: {score}\n"
                summary += "\n"

            # Add areas needing improvement
            vitals_issues = result.get("Vitals Needing Improvement", [])
            lifestyle_issues = result.get("Lifestyle Factors Needing Improvement", [])
            test_issues = result.get("Test Results Needing Improvement", [])

            if vitals_issues or lifestyle_issues or test_issues:
                summary += "**Areas Needing Improvement:**\n"

                if vitals_issues:
                    summary += "Vitals:\n"
                    for issue in vitals_issues:
                        summary += f"- {issue}\n"

                if lifestyle_issues:
                    summary += "Lifestyle Factors:\n"
                    for issue in lifestyle_issues:
                        summary += f"- {issue}\n"

                if test_issues:
                    summary += "Test Results:\n"
                    for issue in test_issues:
                        summary += f"- {issue}\n"

                summary += "\n"

            # Add improvement tips
            if "Improvement Tips" in result and result["Improvement Tips"]:
                summary += "**Personalized Recommendations:**\n"
                for tip in result["Improvement Tips"]:
                    summary += f"{tip}\n"

            # Add device recommendation prompt (following agent_app pattern)
            device_prompt = result.get("device_recommendation_prompt", "")
            if device_prompt:
                summary += device_prompt

            # Add assistant message
            chat_histories[user_key].append({
                "role": "assistant",
                "content": summary
            })

        return result
    except Exception as e:
        error_msg = f"Error generating real-time health score: {str(e)}"
        logging.error(error_msg)
        return {"error": error_msg}

@app.post("/device-recommendations")
async def device_recommendations_endpoint(request: DeviceRecommendationRequest):
    """Suggest connected health devices based on missing or abnormal vitals with test-specific recommendations"""
    try:
        user_id = request.user_id
        health_data = request.health_data
        test_type = request.test_type if hasattr(request, 'test_type') else None
        test_data = request.test_data if hasattr(request, 'test_data') else None

        # If test_data is provided, use it as the primary health data
        if test_data:
            health_data = test_data
        # If no health data provided, use stored data if available
        elif not health_data:
            # Convert user_id to proper key format
            user_key = convert_user_id_to_key(user_id)
            if user_key in user_health_data:
                # Try to get the most recent health data based on test_type if provided
                if test_type and test_type in user_health_data[user_key]:
                    health_data = user_health_data[user_key][test_type]["data"]
                # Otherwise fall back to general health data
                elif "health_score" in user_health_data[user_key]:
                    health_data = user_health_data[user_key]["health_score"]["data"]
                elif "realtime_health_score" in user_health_data[user_key]:
                    health_data = user_health_data[user_key]["realtime_health_score"]["data"]
            else:
                # Use default health data structure if no data is available
                health_data = get_default_health_data()["data"]

        if not health_data:
            return {"error": "No health data provided or found in user records."}

        # Process health data to handle "Unknown" values
        processed_health_data = {}
        for key, value in health_data.items():
            # Skip "Unknown" values for all fields
            if value == "Unknown":
                continue
            # Skip None values
            elif value is None or value == "" or value == "null":
                continue
            # Handle test results with string values
            elif key in ["Malaria", "Widal Test", "Hepatitis B", "Voluntary Serology"]:
                processed_health_data[key] = value
            # Convert numeric values to float
            else:
                try:
                    processed_health_data[key] = float(value)
                except (ValueError, TypeError):
                    # If conversion fails, keep the original value
                    processed_health_data[key] = value

        # Format data for the tool, including test_type if available
        tool_input = {
            "data": processed_health_data
        }
        if test_type:
            tool_input["test_type"] = test_type

        health_data_json = json.dumps(tool_input)

        # Use the device recommender tool
        result = json.loads(recommend_health_devices(health_data_json))

        # Add test_type to the result
        if test_type:
            result["test_type"] = test_type

        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        # Save device recommendations to user health data store
        if user_key not in user_health_data:
            user_health_data[user_key] = {}
        user_health_data[user_key]["device_recommendations"] = {
            "data": processed_health_data,
            "test_type": test_type,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        logging.info(f"Saved device recommendations for user {user_id} with test_type: {test_type}")

        # Update chat history if user exists
        if user_id in chat_histories:
            # Create a test-specific message
            if test_type:
                test_type_name = {
                    "vital_signs": "vital signs",
                    "kidney_function": "kidney function",
                    "lipid_profile": "lipid profile",
                    "lung_capacity": "lung capacity",
                    "test_results": "test results",
                    "realtime_health_score": "health score"
                }.get(test_type, "health")

                user_message = f"Can you recommend health devices that would help me monitor my {test_type_name}?"
            else:
                user_message = "Can you recommend health devices that would help me monitor my health?"

            # Add user message
            chat_histories[user_id].append({
                "role": "user",
                "content": user_message
            })

            # Create a test-specific title for the response
            if test_type:
                test_type_name = {
                    "vital_signs": "Vital Signs",
                    "kidney_function": "Kidney Function",
                    "lipid_profile": "Cardiovascular Health",
                    "lung_capacity": "Respiratory Health",
                    "test_results": "Test Results",
                    "realtime_health_score": "Health Score"
                }.get(test_type, "Health")

                summary = f"Based on your {test_type_name.lower()} data, here are my device recommendations for monitoring your {test_type_name.lower()}:\n\n"
            else:
                summary = "Based on your health data, here are my device recommendations:\n\n"

            # Add the recommendation summary
            if "recommendation_summary" in result:
                summary += result["recommendation_summary"]
            else:
                # Fallback to a more basic summary if the detailed one isn't available
                missing_vitals = result.get("missing_vitals", [])
                abnormal_vitals = result.get("abnormal_vitals", [])

                if missing_vitals:
                    summary += "**Recommended measurements to track:**\n"
                    for vital in missing_vitals:
                        summary += f"- {vital}\n"
                    summary += "\n"

                if abnormal_vitals:
                    summary += "**Key metrics to monitor closely:**\n"
                    for vital in abnormal_vitals:
                        summary += f"- {vital}\n"
                    summary += "\n"

                # Add test-specific device recommendations if available
                test_specific_devices = result.get("test_specific_devices", [])
                if test_type and test_specific_devices:
                    test_type_name = {
                        "vital_signs": "Vital Signs",
                        "kidney_function": "Kidney Function",
                        "lipid_profile": "Cardiovascular Health",
                        "lung_capacity": "Respiratory Health",
                        "test_results": "Test Results",
                        "realtime_health_score": "Health Score"
                    }.get(test_type, "Health")

                    summary += f"**Recommended devices for {test_type_name} monitoring:**\n"
                    for device in test_specific_devices[:3]:  # Limit to top 3
                        summary += f"- {device['name']} - {device['description']}\n"
                        summary += f"  Price range: {device['price_range']}\n"
                        summary += f"  Recommended brands: {', '.join(device['recommended_brands'][:3])}\n"
                    summary += "\n"

                # Add device recommendations
                single_devices = result.get("single_device_recommendations", [])
                multi_devices = result.get("multi_device_recommendations", [])

                if multi_devices:
                    summary += "**Recommended multi-function devices:**\n"
                    for device in multi_devices[:2]:  # Limit to top 2
                        summary += f"- {device['name']} - {device['description']}\n"
                        summary += f"  Price range: {device['price_range']}\n"
                        summary += f"  Recommended brands: {', '.join(device['recommended_brands'][:3])}\n"
                    summary += "\n"

                if single_devices:
                    summary += "**Recommended single-function devices:**\n"
                    for device in single_devices[:3]:  # Limit to top 3
                        summary += f"- {device['name']} - {device['description']}\n"
                        summary += f"  Price range: {device['price_range']}\n"
                        summary += f"  Recommended brands: {', '.join(device['recommended_brands'][:3])}\n"

            # Add assistant message
            chat_histories[user_id].append({
                "role": "assistant",
                "content": summary
            })

        return result
    except Exception as e:
        error_msg = f"Error generating device recommendations: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        return {"error": error_msg}

@app.post("/device-confirmation")
async def device_confirmation_endpoint(request: DeviceConfirmationRequest):
    """Handle user confirmation for device recommendations and provide redirect to turbomedics.com/products"""
    try:
        user_id = request.user_id
        confirmed = request.confirmed

        # Create input data for the confirmation tool
        input_data = {"confirmed": confirmed}

        # Use the device confirmation tool
        result = json.loads(confirm_device_recommendation(json.dumps(input_data)))

        # Update chat history if user exists
        if user_id in chat_histories:
            if confirmed:
                # Add user confirmation message
                chat_histories[user_id].append({
                    "role": "user",
                    "content": "Yes, I'd like to explore these health devices."
                })

                # Add assistant response with redirect information
                response_message = result.get("message", "")
                redirect_message = result.get("redirect_message", "")

                if redirect_message:
                    response_message += f"\n\n{redirect_message}"

                # Add store features if available
                if "additional_info" in result and "store_features" in result["additional_info"]:
                    response_message += "\n\n**Store Features:**\n"
                    for feature in result["additional_info"]["store_features"]:
                        response_message += f"• {feature}\n"

                chat_histories[user_id].append({
                    "role": "assistant",
                    "content": response_message
                })
            else:
                # Add user decline message
                chat_histories[user_id].append({
                    "role": "user",
                    "content": "No, I don't need device recommendations right now."
                })

                # Add assistant response for decline
                response_message = result.get("message", "")
                if "alternative_suggestions" in result:
                    response_message += "\n\n**Alternative options:**\n"
                    for suggestion in result["alternative_suggestions"]:
                        response_message += f"• {suggestion}\n"

                chat_histories[user_id].append({
                    "role": "assistant",
                    "content": response_message
                })

        return result
    except Exception as e:
        error_msg = f"Error processing device confirmation: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        return {"error": error_msg}

@app.post("/symptom-checker")
async def symptom_checker_endpoint(request: SymptomCheckerRequest):
    """Analyze symptoms and provide potential diagnoses, recommended tests, and home care advice"""
    try:
        user_id = request.user_id
        symptoms = request.symptoms

        if not symptoms:
            return {"error": "No symptoms provided."}

        # Create input data for the symptom checker
        input_data = {
            "symptoms": symptoms,
            "age": request.age,
            "sex": request.sex,
            "duration": request.duration,
            "severity": request.severity
        }

        # Use the symptom checker tool
        result = json.loads(analyze_symptoms(json.dumps(input_data)))

        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        # Save symptom checker data to user health data store
        if user_key not in user_health_data:
            user_health_data[user_key] = {}
        user_health_data[user_key]["symptom_checker"] = {
            "data": input_data,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        logging.info(f"Saved symptom checker data for user {user_id}")

        # Update chat history if user exists
        if user_id in chat_histories:
            # Add user message
            chat_histories[user_id].append({
                "role": "user",
                "content": f"I'm experiencing these symptoms: {symptoms}"
            })

            # Create a summary of the analysis for the assistant's response
            summary = "Based on the symptoms you've described, here's my assessment:\n\n"

            # Emergency level alerts disabled - no longer showing urgent medical attention alerts
            # if result.get("urgency_level") == "emergency":
            #     summary += "🚨 **URGENT MEDICAL ATTENTION NEEDED** 🚨\n\n"
            #     summary += f"{result.get('message', 'Please seek immediate medical care.')}\n\n"
            #     summary += f"{result.get('recommendation', '')}\n\n"

            #     # Add assistant message
            #     chat_histories[user_id].append({
            #         "role": "assistant",
            #         "content": summary
            #     })

            #     return result

            # Add potential conditions
            potential_conditions = result.get("potential_conditions", [])
            if potential_conditions:
                summary += "**Potential conditions to discuss with your healthcare provider:**\n"
                for condition in potential_conditions:
                    summary += f"- {condition}\n"
                summary += "\n"

            # Add urgency level with appropriate emoji
            urgency_level = result.get("urgency_level", "low")
            if urgency_level == "high":
                summary += "**Urgency Level: 🚨 High** - Please consult with a healthcare provider soon.\n\n"
            elif urgency_level == "moderate":
                summary += "**Urgency Level: ⚠️ Moderate** - Consider scheduling an appointment with your healthcare provider.\n\n"
            else:
                summary += "**Urgency Level: ✅ Low** - Monitor your symptoms and consult a healthcare provider if they worsen.\n\n"

            # Add recommended tests
            recommended_tests = result.get("recommended_tests", [])
            if recommended_tests:
                summary += "**Tests your healthcare provider might consider:**\n"
                for test in recommended_tests:
                    summary += f"- {test}\n"
                summary += "\n"

            # Add home care recommendations
            home_care = result.get("home_care_recommendations", [])
            if home_care:
                summary += "**Self-care recommendations:**\n"
                for care in home_care:
                    summary += f"- {care}\n"
                summary += "\n"

            # Add disclaimer
            summary += "**Important:** This is not a medical diagnosis. The information provided is for educational purposes only and should not replace professional medical advice. Please consult with a healthcare provider for proper evaluation and treatment."

            # Add assistant message
            chat_histories[user_id].append({
                "role": "assistant",
                "content": summary
            })

        return result
    except Exception as e:
        error_msg = f"Error analyzing symptoms: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        return {"error": error_msg}

@app.post("/lab-test-explainer")
async def lab_test_explainer_endpoint(request: LabTestExplainerRequest):
    """Explain lab tests, including what they measure, normal ranges, and patient education"""
    try:
        user_id = request.user_id

        # If list_tests is True, return a list of available tests
        if request.list_tests:
            logging.info("Listing available lab tests")
            result_json = list_available_tests()
            result = json.loads(result_json)
            return result

        # If test_name is provided, explain the specific test
        elif request.test_name:
            logging.info(f"Explaining lab test: {request.test_name}")
            result_json = explain_lab_test(request.test_name)
            result = json.loads(result_json)

            # Update chat history if user exists
            if user_id in chat_histories:
                # Add user message
                chat_histories[user_id].append({
                    "role": "user",
                    "content": f"Can you explain what the {request.test_name} test measures and what the normal ranges are?"
                })

                # Create a summary of the lab test explanation for the assistant's response
                if result.get("found", False):
                    summary = f"**{result['test_name']} Explained**\n\n"

                    # Add note if this was a fuzzy match
                    if "note" in result:
                        summary += f"*{result['note']}*\n\n"

                    # Add description
                    summary += f"{result['description']}\n\n"

                    # Add what it measures
                    if "what_it_measures" in result:
                        summary += "**What it measures:**\n"
                        for item in result["what_it_measures"]:
                            summary += f"- {item}\n"
                        summary += "\n"

                    # Add normal ranges
                    if "normal_ranges" in result:
                        summary += "**Normal ranges:**\n"
                        for param, range_val in result["normal_ranges"].items():
                            summary += f"- {param}: {range_val}\n"
                        summary += "\n"

                    # Add when it's ordered
                    if "when_ordered" in result:
                        summary += "**When this test is ordered:**\n"
                        for item in result["when_ordered"][:3]:  # Limit to 3 items
                            summary += f"- {item}\n"
                        summary += "\n"

                    # Add patient education
                    if "patient_education" in result:
                        summary += f"**Patient education:**\n{result['patient_education']}\n\n"

                    # Add preparation
                    if "preparation" in result:
                        summary += f"**Preparation:**\n{result['preparation']}\n\n"

                    # Add interpretation
                    if "interpretation" in result:
                        summary += f"**Interpretation:**\n{result['interpretation']}"
                else:
                    summary = f"I'm sorry, but I don't have information about the '{request.test_name}' test in my database. Please try another test name or ask for a list of available tests."

                # Add assistant message
                chat_histories[user_id].append({
                    "role": "assistant",
                    "content": summary
                })

            return result

        # If neither list_tests nor test_name is provided, return an error
        else:
            error_msg = "Either test_name or list_tests must be provided"
            logging.error(error_msg)
            return {"error": error_msg}
    except Exception as e:
        error_msg = f"Error processing lab test explainer request: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        return {"error": error_msg}

# Removed /chronic_tracker endpoint - using /track-chronic-condition instead

def process_chronic_tracker(user_id, condition_data):
    """Process chronic condition tracking data"""
    try:
        logging.info(f"Processing chronic tracker data for user {user_id}: {condition_data}")

        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        # Get historical data for this user and condition type
        historical_data = []
        condition_type = condition_data.get("condition_type", "")
        logging.info(f"Condition type: {condition_type}")

        if user_key in user_health_data and "chronic_tracker" in user_health_data[user_key]:
            # Filter historical data by condition type
            for entry in user_health_data[user_key]["chronic_tracker"]:
                if entry["data"].get("condition_type") == condition_type:
                    historical_data.append({
                        "condition_data": entry["data"],
                        "timestamp": entry["timestamp"]
                    })

        # Prepare input data for the tool
        input_data = {
            "user_id": user_id,
            "condition_data": condition_data,
            "historical_data": historical_data
        }

        # Use the chronic tracker tool
        logging.info(f"Calling chronic_tracker_tool with input: {json.dumps(input_data)}")
        try:
            tool_result = chronic_tracker_tool(json.dumps(input_data))
            logging.info(f"Received raw result from chronic_tracker_tool: {tool_result}")
            result = json.loads(tool_result)
            logging.info(f"Parsed result: {json.dumps(result)}")
        except Exception as e:
            logging.error(f"Error calling chronic_tracker_tool: {str(e)}")
            logging.error(traceback.format_exc())
            return {"error": f"Error calling chronic_tracker_tool: {str(e)}"}

        return result
    except Exception as e:
        error_msg = f"Error processing chronic tracker data: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

# Add a new endpoint for chronic condition tracking
@app.post("/track-chronic-condition")
async def track_chronic_condition(request: ChronicTrackerRequest):
    """Track chronic conditions over time, analyze trends, and provide personalized feedback with support for daily/weekly tracking"""
    try:
        logging.info(f"Received chronic condition tracking request: {request}")
        user_id = request.user_id
        condition_data = request.condition_data
        tracking_frequency = request.tracking_frequency
        measurement_date = request.measurement_date

        if not condition_data:
            return {"error": "No condition data provided."}

        # Ensure condition_type is provided
        if "condition_type" not in condition_data:
            return {"error": "condition_type is required in condition_data."}

        # Add tracking frequency and measurement date to condition data
        condition_data["tracking_frequency"] = tracking_frequency

        # Use provided measurement date or current time
        if measurement_date:
            condition_data["measurement_date"] = measurement_date
        else:
            condition_data["measurement_date"] = datetime.now().isoformat()

        # Process the chronic condition data
        result = process_chronic_tracker(user_id, condition_data)

        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        # Save chronic tracker data to user health data store
        if user_key not in user_health_data:
            user_health_data[user_key] = {}

        # Initialize chronic_tracker as a list if it doesn't exist
        if "chronic_tracker" not in user_health_data[user_key]:
            user_health_data[user_key]["chronic_tracker"] = []

        # Add the new entry to the list
        user_health_data[user_key]["chronic_tracker"].append({
            "data": condition_data,
            "result": result,
            "timestamp": datetime.now().isoformat()
        })

        logging.info(f"Saved chronic tracker data for user {user_id}")

        # Update chat history if user exists
        if user_id in chat_histories:
            condition_type = condition_data.get("condition_type", "")
            frequency_text = ""

            # Add frequency information to the user message
            if tracking_frequency == "daily":
                frequency_text = "daily "
            elif tracking_frequency == "weekly":
                frequency_text = "weekly "
            elif tracking_frequency == "monthly":
                frequency_text = "monthly "

            # Add user message
            chat_histories[user_id].append({
                "role": "user",
                "content": f"I've submitted my {frequency_text}{condition_type} tracking data for analysis."
            })

            # Create a summary of the analysis for the assistant's response
            summary = f"I've analyzed your {condition_type} tracking data.\n\n"

            # Add summary from the result
            if "summary" in result:
                summary += result["summary"]
            else:
                # Create a basic summary if the detailed one isn't available
                if "current_analysis" in result:
                    current = result["current_analysis"]

                    # Add glucose status if available
                    if "glucose" in current:
                        glucose_status = current["glucose"]["status"]
                        glucose_desc = current["glucose"]["description"]
                        summary += f"**Glucose:** {glucose_status.title()} - {glucose_desc}\n\n"

                    # Add HbA1c status if available
                    if "hba1c" in current:
                        hba1c_status = current["hba1c"]["status"]
                        hba1c_desc = current["hba1c"]["description"]
                        summary += f"**HbA1c:** {hba1c_status.title()} - {hba1c_desc}\n\n"

                # Add trend analysis if available
                if "trend_analysis" in result:
                    trends = result["trend_analysis"]

                    # Add tracking frequency and consistency information
                    if "tracking_frequency" in trends:
                        freq = trends["tracking_frequency"]
                        if freq != "as_needed":
                            summary += f"**Tracking Frequency:** {freq.title()}\n"

                            if "consistency_score" in trends:
                                score = trends["consistency_score"]
                                if score >= 90:
                                    summary += f"**Consistency:** Excellent ({score}/100)\n\n"
                                elif score >= 75:
                                    summary += f"**Consistency:** Good ({score}/100)\n\n"
                                elif score >= 50:
                                    summary += f"**Consistency:** Fair ({score}/100)\n\n"
                                else:
                                    summary += f"**Consistency:** Needs improvement ({score}/100)\n\n"

                    # Add glucose trend information
                    if "glucose" in trends and trends["glucose"]:
                        glucose_trend = trends["glucose"]["direction"]
                        glucose_desc = trends["glucose"]["description"]
                        summary += f"**Glucose Trend:** {glucose_trend.title()} - {glucose_desc}\n\n"

                        # Add frequency-specific insights if available
                        if "frequency_analysis" in trends["glucose"] and trends["glucose"]["frequency_analysis"]:
                            analysis = trends["glucose"]["frequency_analysis"]

                            # Add daily pattern insights
                            if "insights" in analysis and analysis["insights"]:
                                summary += "**Pattern Analysis:**\n"
                                for i, insight in enumerate(analysis["insights"][:2], 1):  # Limit to first 2 insights
                                    summary += f"- {insight}\n"
                                summary += "\n"

                    # Add HbA1c trend information
                    if "hba1c" in trends and trends["hba1c"]:
                        hba1c_trend = trends["hba1c"]["direction"]
                        hba1c_desc = trends["hba1c"]["description"]
                        summary += f"**HbA1c Trend:** {hba1c_trend.title()} - {hba1c_desc}\n\n"

                # Add recommendations
                if "recommendations" in result:
                    recommendations = result["recommendations"]
                    if recommendations:
                        # Filter out section headers (they start with newline)
                        main_recommendations = [rec for rec in recommendations if not rec.startswith("\n")]

                        # Find section indices
                        tracking_section_index = -1
                        continuous_care_section_index = -1

                        for i, rec in enumerate(recommendations):
                            if rec == "\nFor better tracking:":
                                tracking_section_index = i
                            elif rec == "\nFor continuous care:":
                                continuous_care_section_index = i

                        # Add main recommendations
                        summary += "**Recommendations:**\n"
                        end_index = min(5, tracking_section_index if tracking_section_index > 0 else len(main_recommendations))
                        for i, rec in enumerate(main_recommendations[:end_index], 1):
                            summary += f"{i}. {rec}\n"

                        # Add tracking recommendations section if it exists
                        if tracking_section_index > 0:
                            summary += "\n**For Better Tracking:**\n"
                            start_idx = tracking_section_index + 1
                            end_idx = continuous_care_section_index if continuous_care_section_index > 0 else len(recommendations)
                            for rec in recommendations[start_idx:end_idx]:
                                if not rec.startswith("\n"):  # Skip any nested section headers
                                    summary += f"• {rec}\n"

                        # Add continuous care recommendations section if it exists
                        if continuous_care_section_index > 0:
                            summary += "\n**For Continuous Care:**\n"
                            for rec in recommendations[continuous_care_section_index+1:]:
                                if not rec.startswith("\n"):  # Skip any nested section headers
                                    summary += f"• {rec}\n"

            # Add assistant message
            chat_histories[user_id].append({
                "role": "assistant",
                "content": summary
            })

        return result
    except Exception as e:
        error_msg = f"Error tracking chronic condition: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        return {"error": error_msg}

# Add a simple test endpoint
@app.get("/test-endpoint")
async def test_endpoint():
    """Test endpoint to verify server functionality"""
    return {"message": "Test endpoint is working"}

# Add follow-up reminder endpoint
@app.post("/followup-reminder")
async def followup_reminder_endpoint(request: FollowupReminderRequest):
    """Generate follow-up reminders based on past health data and abnormal values, with optional test-specific filtering"""
    try:
        user_id = request.user_id
        health_data = request.health_data
        test_type = request.test_type

        # If health data is not provided, use stored health data
        if not health_data:
            # Convert user_id to proper key format
            user_key = convert_user_id_to_key(user_id)
            if user_key in user_health_data:
                # If test_type is provided, only use that specific test data
                if test_type and test_type in user_health_data[user_key]:
                    health_data = user_health_data[user_key][test_type].get("data", {})
                    logging.info(f"Using test-specific data for {test_type}")
                else:
                    # Combine all health data for the user
                    health_data = {}

                    # Add vital signs
                    if "vital_signs" in user_health_data[user_key]:
                        health_data.update(user_health_data[user_key]["vital_signs"].get("data", {}))

                    # Add health score data
                    if "health_score" in user_health_data[user_key]:
                        health_data.update(user_health_data[user_key]["health_score"].get("data", {}))

                    # Add kidney function data
                    if "kidney_function" in user_health_data[user_key]:
                        health_data.update(user_health_data[user_key]["kidney_function"].get("data", {}))

                    # Add lipid profile data
                    if "lipid_profile" in user_health_data[user_key]:
                        health_data.update(user_health_data[user_key]["lipid_profile"].get("data", {}))

                    # Add lung capacity data
                    if "lung_capacity" in user_health_data[user_key]:
                        health_data.update(user_health_data[user_key]["lung_capacity"].get("data", {}))

                    # Add test results data
                    if "test_results" in user_health_data[user_key]:
                        health_data.update(user_health_data[user_key]["test_results"].get("data", {}))

        # If no health data is available, return an error
        if not health_data:
            return {
                "error": "No health data available for this user. Please enter some health data first.",
                "summary": "I don't have enough health data to generate follow-up reminders. Please use the quick action buttons to enter your health data first."
            }

        # Prepare input data for the tool
        input_data = {
            "user_id": user_id,
            "health_data": health_data
        }

        # Add test_type if provided
        if test_type:
            input_data["test_type"] = test_type

        # Use the followup reminder tool
        logging.info(f"Calling followup_reminder_tool with input: {json.dumps(input_data)}")
        try:
            tool_result = followup_reminder_tool(json.dumps(input_data))
            logging.info(f"Received raw result from followup_reminder_tool: {tool_result}")
            result = json.loads(tool_result)
            logging.info(f"Parsed result: {json.dumps(result)}")
        except Exception as e:
            logging.error(f"Error calling followup_reminder_tool: {str(e)}")
            logging.error(traceback.format_exc())
            return {"error": f"Error calling followup_reminder_tool: {str(e)}"}

        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        # Save follow-up reminder data to user health data store
        if user_key not in user_health_data:
            user_health_data[user_key] = {}

        user_health_data[user_key]["followup_reminder"] = {
            "reminders": result.get("reminders", []),
            "follow_up_schedule": result.get("follow_up_schedule", {}),
            "care_continuity": result.get("care_continuity", []),
            "timestamp": datetime.now().isoformat()
        }

        logging.info(f"Saved follow-up reminder data for user {user_id}")

        # Update chat history if user exists
        if user_id in chat_histories:
            # Create a test-specific message if test_type is provided
            if test_type:
                test_type_name = {
                    "vital_signs": "vital signs",
                    "kidney_function": "kidney function",
                    "lipid_profile": "lipid profile",
                    "lung_capacity": "respiratory health",
                    "test_results": "test results",
                    "realtime_health_score": "health score"
                }.get(test_type, "health")

                user_message = f"I'd like to see follow-up reminders for my {test_type_name} results."
            else:
                user_message = "I'd like to see my follow-up reminders and care continuity plan."

            # Add user message
            chat_histories[user_id].append({
                "role": "user",
                "content": user_message
            })

            # Create a test-specific response if test_type is provided
            if test_type:
                test_type_name = {
                    "vital_signs": "Vital Signs",
                    "kidney_function": "Kidney Function",
                    "lipid_profile": "Cardiovascular Health",
                    "lung_capacity": "Respiratory Health",
                    "test_results": "Test Results",
                    "realtime_health_score": "Health Score"
                }.get(test_type, "Health")

                # Add test_type to the result for the summary
                result["test_type"] = test_type

                # Add assistant message with test-specific title
                chat_histories[user_id].append({
                    "role": "assistant",
                    "content": f"**{test_type_name} Follow-up Reminders**\n\n{result.get('summary', 'Here are your follow-up reminders based on your test results.')}"
                })
            else:
                # Add generic assistant message
                chat_histories[user_id].append({
                    "role": "assistant",
                    "content": result.get("summary", "Here are your follow-up reminders and care continuity plan.")
                })

        return result
    except Exception as e:
        error_msg = f"Error generating follow-up reminders: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        return {"error": error_msg}

@app.post("/chronic-risk")
async def chronic_risk_endpoint(request: ChronicRiskRequest):
    try:
        user_id = request.user_id
        data = request.chronic_data

        # Run risk prediction
        result = predict_chronic_risk(data)

        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        # Save to health data
        if user_key not in user_health_data:
            user_health_data[user_key] = {}
        user_health_data[user_key]["chronic_risk"] = {
            "input": data,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

        # Log chat interaction if enabled
        if user_key in chat_histories:
            chat_histories[user_key].append({
                "role": "user",
                "content": f"My chronic risk inputs are: {json.dumps(data)}"
            })
            chat_histories[user_key].append({
                "role": "assistant",
                "content": f"Here is your chronic disease risk prediction:\n{json.dumps(result, indent=2)}"
            })

        return result
    except Exception as e:
        logging.error(f"Chronic risk analysis failed for {request.user_id}: {str(e)}")
        return {"error": str(e)}

@app.post("/summarize-medical-doc")
async def summarize_medical_doc(
    user_id: str = Form(...),
    model: Optional[str] = Form(DEFAULT_MODEL),  # Optional model selection
    file: UploadFile = File(...)
):
    try:
        # Use the updated extract_text_from_upload function that supports both PDF and DOCX
        raw_text = extract_text_from_upload(file)

        if len(raw_text) < 100:
            return {"error": "Unable to extract enough readable text. Please try another document."}

        # Use selected or default model
        summary = summarize_medical_text(raw_text, model=model)

        # Determine file type
        file_type = "PDF" if file.filename.lower().endswith(".pdf") else "DOCX" if file.filename.lower().endswith(".docx") else "document"

        # Store in chat history
        if user_id in chat_histories:
            chat_histories[user_id].append({"role": "user", "content": f"I've uploaded a medical {file_type} document: {file.filename}"})
            chat_histories[user_id].append({"role": "assistant", "content": summary})

        return {
            "summary": summary,
            "length": len(raw_text),
            "file_type": file_type,
            "filename": file.filename,
            "success": True
        }

    except ValueError as ve:
        # Handle unsupported file type error
        logging.error(f"Unsupported file type: {ve}")
        return {"error": str(ve)}
    except Exception as e:
        logging.error(f"Error summarizing doc: {e}")
        return {"error": str(e)}

@app.post("/track-lifestyle")
async def track_lifestyle_habits(request: LifestyleHabitRequest):
    try:
        user_id = request.user_id
        habits = request.habits

        record_habits(user_id, habits)
        summary = compute_weekly_habit_summary(user_id)
        recommendations = generate_lifestyle_recommendations(summary)

        # Update chat history
        if user_id in chat_histories:
            chat_histories[user_id].append({"role": "user", "content": f"My lifestyle habits are: {habits}"})
            chat_histories[user_id].append({"role": "assistant", "content": "I've logged your habits and generated a weekly summary."})

        return {
            "weekly_summary": summary,
            "recommendations": recommendations
        }

    except Exception as e:
        logging.error(f"Error processing lifestyle habits: {str(e)}")
        return {"error": str(e)}


@app.post("/weekly-digest")
async def summarize_weekly_vitals(request: DigestRequest):
    try:
        user_id = request.user_id
        data = request.vital_signs
        timestamp = datetime.now().isoformat()

        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        # Add the new vitals to the user's history
        if user_key not in user_health_data:
            user_health_data[user_key] = []

        user_health_data[user_key].append({**data, "timestamp": timestamp})

        # Call digest generator tool
        digest = generate_weekly_digest(user_key, user_health_data)

        return {
            "current_vitals": data,
            "weekly_digest": digest,
            "records_logged": len(user_health_data[user_key])
        }

    except Exception as e:
        return {"error": str(e)}

@app.post("/mental-health-assessment")
async def mental_health_assessment_endpoint(request: Request):
    """
    Comprehensive Mental Health Assessment endpoint
    Includes stress/burnout, PHQ-9, GAD-7, and ML risk prediction
    """
    try:
        # Use the new JSON parsing utility with automatic cleaning and validation
        request_data = await parse_json_request(request, required_fields=["user_id", "assessment_data"])

        user_id = request_data["user_id"]
        assessment_data = request_data["assessment_data"]

        logging.info(f"Processing mental health assessment for user {user_id}")

        # Debug: Try to serialize assessment data to catch JSON issues
        try:
            serialized_data = json.dumps(assessment_data)
            logging.info(f"Assessment data serialized successfully: {serialized_data}")
        except (TypeError, ValueError) as json_error:
            logging.error(f"JSON serialization error in assessment data: {json_error}")
            logging.error(f"Assessment data type: {type(assessment_data)}")
            logging.error(f"Assessment data keys and types: {[(k, type(v)) for k, v in assessment_data.items()]}")
            return {"error": f"Invalid assessment data format: {json_error}"}

        # Initialize the mental health assessment tool
        mental_health_tool = MentalHealthAssessmentTool()

        # Validate that country is provided
        if "country" not in assessment_data or not assessment_data["country"]:
            return {"error": "Country is required for mental health assessment and crisis resource recommendations"}

        # Perform comprehensive assessment
        result = mental_health_tool.comprehensive_assessment(assessment_data)

        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        # Save assessment results to user health data
        if user_key not in user_health_data:
            user_health_data[user_key] = {}

        user_health_data[user_key]["mental_health_assessment"] = {
            "input": assessment_data,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

        # Update chat history if user exists
        if user_key in chat_histories:
            # Add user message
            chat_histories[user_key].append({
                "role": "user",
                "content": "I'd like to complete a comprehensive mental health assessment."
            })

            # Create a summary for the chat
            summary = result.get("summary", "Mental health assessment completed.")
            risk_level = result.get("assessments", {}).get("ml_risk_prediction", {}).get("risk_level", "Unknown")

            # Add assistant message with assessment summary
            chat_histories[user_key].append({
                "role": "assistant",
                "content": f"**Mental Health Assessment Complete**\n\n{summary}\n\n**Risk Level**: {risk_level}\n\nI've provided personalized recommendations and follow-up reminders based on your assessment. If you're experiencing a mental health crisis, please reach out to the crisis resources provided."
            })

        logging.info(f"Mental health assessment completed for user {user_id}")
        return result

    except Exception as e:
        error_msg = f"Error in mental health assessment: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        return {"error": error_msg}


@app.get("/mental-health-countries")
async def get_mental_health_countries():
    """
    Get list of supported countries for mental health crisis resources
    """
    try:
        from tools.tools_mental_health_assessment import MentalHealthAssessmentTool
        mental_health_tool = MentalHealthAssessmentTool()
        countries = mental_health_tool.get_supported_countries()

        return {
            "supported_countries": countries,
            "total_countries": len(countries),
            "note": "These countries have specific crisis resources available. Other countries will receive generic international resources."
        }
    except Exception as e:
        logging.error(f"Error getting supported countries: {str(e)}")
        return {"error": f"Failed to get supported countries: {str(e)}"}

@app.post("/liver-function/manual")
async def analyze_liver_manual(request: LiverFunctionAssessmentRequest):
    try:
        user_id = request.user_id
        values = {
        "ALT (SGPT)": request.lft_data.ALT_SGPT,
        "AST (SGOT)": request.lft_data.AST_SGOT,
        "ALP": request.lft_data.ALP,
        "GGT": request.lft_data.GGT,
        "Total Bilirubin": request.lft_data.Total_Bilirubin,
        "Direct Bilirubin": request.lft_data.Direct_Bilirubin,
        "Albumin": request.lft_data.Albumin,
        "INR": request.lft_data.INR,
        "Ammonia": request.lft_data.Ammonia,
        "LDH": request.lft_data.LDH,
        "Globulin": request.lft_data.Globulin,
        "A/G Ratio": request.lft_data.AG_Ratio,
        "ALT:AST Ratio": request.lft_data.ALT_AST_Ratio,
        "Indirect Bilirubin": request.lft_data.Indirect_Bilirubin,
        "Total Protein": request.lft_data.Total_Protein,
    }

        result = analyze_liver_function(
            extracted_values=values,
            dietary_habits=request.lft_data.dietary_habits,
            medications=request.lft_data.medications,
            symptoms=request.lft_data.symptoms,
            hepatitis_markers=request.lft_data.hepatitis_markers,
            smoking_alcohol_use=request.lft_data.smoking_alcohol_use,
            medical_conditions=request.lft_data.medical_conditions,
            input_method="Manual Entry"
        )

        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        # Store in user memory
        if user_key not in user_health_data:
            user_health_data[user_key] = {}
        user_health_data[user_key]["liver_function"] = {
            "data": values,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

        # Add to chat summary
        if user_key in chat_histories:
            chat_histories[user_key].append({"role": "user", "content": "Please assess my liver function."})
            summary = "\n".join(result["parameter_status"])
            summary += f"\n\n**Risk Level**: {result['risk_level']}\n**Confidence**: {result['confidence_level']}\n\n**Recommendations:**\n- " + "\n- ".join(result["recommendations"])

            # Add device recommendation prompt (following agent_app pattern)
            device_prompt = result.get("device_recommendation_prompt", "")
            if device_prompt:
                summary += device_prompt

            chat_histories[user_key].append({"role": "assistant", "content": summary})

        return result

    except Exception as e:
        logging.error(f"Liver Function Error: {str(e)}")
        return {"error": str(e)}


@app.post("/liver-function/pdf")
async def analyze_liver_from_pdf(user_id: str, file: UploadFile = File(...)):
    try:
        import pdfplumber
        with pdfplumber.open(file.file) as pdf:
            text = "\n".join(page.extract_text() for page in pdf.pages if page.extract_text())
        values = extract_lft_values(text)

        result = analyze_liver_function(
            extracted_values=values,
            input_method="Upload PDF"
        )

        # Convert user_id to proper key format
        user_key = convert_user_id_to_key(user_id)

        # Store in memory
        if user_key not in user_health_data:
            user_health_data[user_key] = {}
        user_health_data[user_key]["liver_function"] = {
            "data": values,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

        # Chat memory
        if user_key in chat_histories:
            chat_histories[user_key].append({"role": "user", "content": "Please analyze my liver function test PDF."})
            summary = "\n".join(result["parameter_status"])
            summary += f"\n\n**Risk Level**: {result['risk_level']}\n**Confidence**: {result['confidence_level']}\n\n**Recommendations:**\n- " + "\n- ".join(result["recommendations"])

            # Add device recommendation prompt (following agent_app pattern)
            device_prompt = result.get("device_recommendation_prompt", "")
            if device_prompt:
                summary += device_prompt

            chat_histories[user_key].append({"role": "assistant", "content": summary})

        return {"extracted_values": values, **result}

    except Exception as e:
        logging.error(f"Liver Function PDF Error: {str(e)}")
        return {"error": str(e)}

# Reproductive Health Endpoints

@app.post("/postpartum-health")
async def postpartum_health_endpoint(request: PostpartumHealthRequest):
    """Track postpartum recovery for mother and baby health"""
    try:
        user_id = request.user_id
        payload = request.model_dump()
        del payload["user_id"]  # Remove user_id from payload

        result = postpartum_health_tool(user_id, payload)

        # Store in memory
        user_key = convert_user_id_to_key(user_id)
        if user_key not in user_health_data:
            user_health_data[user_key] = {}
        user_health_data[user_key]["postpartum_health"] = {
            "data": payload,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

        # Add to chat history if available
        if user_key in chat_histories:
            chat_histories[user_key].append({"role": "user", "content": "Please analyze my postpartum health data."})
            summary = f"**Postpartum Recovery Analysis**\n\n"
            summary += f"Days since delivery: {result.get('Days Since Delivery', 'N/A')}\n\n"
            if result.get('Flags'):
                summary += "**Health Flags:**\n- " + "\n- ".join(result['Flags']) + "\n\n"
            if result.get('recommendations'):
                summary += "**Recommendations:**\n- " + "\n- ".join(result['recommendations'][:5])  # Limit to first 5
            chat_histories[user_key].append({"role": "assistant", "content": summary})

        return result

    except Exception as e:
        error_msg = f"Error in postpartum health tracking: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

@app.post("/pregnancy-monitoring")
async def pregnancy_monitoring_endpoint(request: PregnancyMonitoringRequest):
    """Monitor pregnancy progress and analyze symptoms"""
    try:
        user_id = request.user_id
        payload = request.model_dump()
        del payload["user_id"]  # Remove user_id from payload

        result = pregnancy_monitoring_tool(user_id, payload)

        # Store in memory
        user_key = convert_user_id_to_key(user_id)
        if user_key not in user_health_data:
            user_health_data[user_key] = {}
        user_health_data[user_key]["pregnancy_monitoring"] = {
            "data": payload,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

        # Add to chat history if available
        if user_key in chat_histories:
            chat_histories[user_key].append({"role": "user", "content": "Please analyze my pregnancy monitoring data."})
            summary = f"**Pregnancy Monitoring Analysis**\n\n"
            summary += f"Gestational Age: {result.get('Gestational Age', 'N/A')}\n\n"
            if result.get('Diagnosis'):
                summary += "**Symptom Analysis:**\n- " + "\n- ".join(result['Diagnosis']) + "\n\n"
            if result.get('recommendations'):
                summary += "**Recommendations:**\n- " + "\n- ".join(result['recommendations'][:5])  # Limit to first 5
            chat_histories[user_key].append({"role": "assistant", "content": summary})

        return result

    except Exception as e:
        error_msg = f"Error in pregnancy monitoring: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

@app.post("/reproductive-health")
async def reproductive_health_endpoint(request: ReproductiveHealthRequest):
    """Unified endpoint for reproductive health tracking - handles both cycle and lifestyle modes"""
    try:
        user_id = request.user_id
        mode = request.mode

        # Prepare payload based on mode
        if mode == "cycle":
            if not request.start_date or not request.period_duration:
                return {"error": "For cycle mode, start_date and period_duration are required"}
            payload = {
                "start_date": request.start_date,
                "period_duration": request.period_duration
            }
            storage_key = "reproductive_health_cycle"
            chat_message = "Please analyze my menstrual cycle data."

        elif mode == "lifestyle":
            if not request.activities:
                return {"error": "For lifestyle mode, activities data is required"}
            payload = {
                "activities": request.activities
            }
            storage_key = "reproductive_health_lifestyle"
            chat_message = "Please analyze my reproductive health lifestyle data."

        else:
            return {"error": "Invalid mode. Use 'cycle' or 'lifestyle'"}

        # Call the reproductive health tracker tool
        result = reproductive_health_tracker_tool(user_id, mode, payload)

        # Store in memory
        user_key = convert_user_id_to_key(user_id)
        if user_key not in user_health_data:
            user_health_data[user_key] = {}
        user_health_data[user_key][storage_key] = {
            "data": payload,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

        # Add to chat history if available
        if user_key in chat_histories:
            chat_histories[user_key].append({"role": "user", "content": chat_message})

            if mode == "cycle":
                summary = f"**Menstrual Cycle Analysis**\n\n"
                if result.get('next_prediction'):
                    pred = result['next_prediction']
                    summary += f"Next period predicted: {pred.get('Next Period Start', 'N/A')}\n"
                    summary += f"Ovulation window: {pred.get('Ovulation Window', 'N/A')}\n\n"
                if result.get('recommendations'):
                    summary += "**Recommendations:**\n- " + "\n- ".join(result['recommendations'][:5])  # Limit to first 5

            elif mode == "lifestyle":
                summary = f"**Reproductive Health Lifestyle Analysis**\n\n"
                if result.get('recommendations'):
                    summary += "**Lifestyle Recommendations:**\n- " + "\n- ".join(result['recommendations'][:8])  # Show more lifestyle tips

            chat_histories[user_key].append({"role": "assistant", "content": summary})

        return result

    except Exception as e:
        error_msg = f"Error in reproductive health tracking ({request.mode}): {str(e)}"
        logging.error(error_msg)
        logging.error(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

# === CHATGPT-STYLE PARALLEL PROCESSING ENDPOINTS ===

async def process_query_parallel(query: str, patient_id: str, session_id: str, model_name: str) -> str:
    """Process a query with parallel execution and smart caching - ChatGPT-style speed"""
    try:
        user_key = (patient_id, session_id)

        # Prepare parallel tasks
        tasks = []

        # Task 1: Get context from vector store
        tasks.append((retrieve_context_cache, (query, model_name), {}))

        # Task 2: Get user health data (if exists)
        if user_key in user_health_data:
            tasks.append((lambda uk: user_health_data.get(uk, {}), (user_key,), {}))

        # Task 3: Detect intent (lightweight operation)
        tasks.append((lambda q: q.lower(), (query,), {}))

        # Execute tasks in parallel
        results = await parallel_processor.run_parallel(tasks)

        # Extract results
        context = results[0] if len(results) > 0 and not isinstance(results[0], Exception) else ""
        user_data = results[1] if len(results) > 1 and not isinstance(results[1], Exception) else {}
        query_lower = results[2] if len(results) > 2 and not isinstance(results[2], Exception) else query.lower()

        logger.info(f"Parallel execution completed - Context: {len(context)} chars, User data: {bool(user_data)}")

        # Continue with intent detection and response generation
        return await _process_query_with_context(query, query_lower, user_key, context, user_data, model_name)

    except Exception as e:
        logger.error(f"Error in parallel query processing: {e}")
        logger.error(traceback.format_exc())
        return f"I apologize, but I encountered an error while processing your query. Please try again."

async def _process_query_with_context(query: str, query_lower: str, user_key: tuple, context: str, user_data: dict, model_name: str) -> str:
    """Process query with provided context and data"""
    try:
        # Intent detection with parallel health analysis if needed
        if any(keyword in query_lower for keyword in ["health score", "analyze", "assessment"]):
            if user_data and "health_data" in user_data:
                # Run health analysis in parallel with response generation
                health_analysis_task = parallel_processor.run_with_timeout(
                    analyze_health_score, 15, user_data["health_data"]
                )
                health_result = await health_analysis_task
                if health_result:
                    return _format_health_analysis_response(health_result)

        # Continue with regular chat processing using context
        return await _generate_chat_response(query, context, user_key, model_name)

    except Exception as e:
        logger.error(f"Error processing query with context: {e}")
        return "I apologize, but I encountered an error. Please try again."

async def _generate_chat_response(query: str, context: str, user_key: tuple, model_name: str) -> str:
    """Generate chat response using Ollama with context"""
    try:
        # Prepare the prompt with context
        system_prompt = f"""You are a friendly, knowledgeable health assistant. Use the following context to provide helpful, accurate health information:

Context: {context[:2000]}  # Limit context to prevent token overflow

Guidelines:
- Be conversational and supportive
- Provide evidence-based health information
- Always recommend consulting healthcare professionals for serious concerns
- Keep responses concise but informative
"""

        # Use Ollama for response generation with timeout
        response_task = parallel_processor.run_with_timeout(
            lambda: ollama.chat(
                model=model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": query}
                ]
            ),
            timeout=30
        )

        response = await response_task
        if response and "message" in response:
            return response["message"]["content"]
        else:
            return "I'm having trouble generating a response right now. Please try again."

    except Exception as e:
        logger.error(f"Error generating chat response: {e}")
        return "I apologize, but I'm having technical difficulties. Please try again."

def _format_health_analysis_response(health_result: dict) -> str:
    """Format health analysis result into a friendly response"""
    try:
        score = health_result.get("Total Score", 0)
        status = health_result.get("Health Status", "Unknown")
        improvements = health_result.get("Vitals Needing Improvement", [])
        tips = health_result.get("Improvement Tips", [])

        response = f"🏥 **Your Health Score: {score}/100 ({status})**\n\n"

        if improvements and improvements != ["None"]:
            response += f"**Areas for improvement:** {', '.join(improvements)}\n\n"

        if tips:
            response += "**Personalized recommendations:**\n"
            for tip in tips[:3]:  # Limit to top 3 tips
                response += f"• {tip}\n"

        return response

    except Exception as e:
        logger.error(f"Error formatting health analysis: {e}")
        return "I've analyzed your health data, but had trouble formatting the results. Please try again."

@app.get("/api/cache-stats")
async def get_cache_stats():
    """Get detailed cache performance statistics"""
    return {
        "embedding_cache": embedding_cache_smart.get_stats(),
        "health_analysis_cache": health_analysis_cache.get_stats(),
        "consultation_cache": consultation_cache.get_stats(),
        "smart_cache": smart_cache.get_stats(),
        "parallel_processor": {
            "max_workers": parallel_processor.max_workers,
            "active": True
        }
    }

# === RUN SERVER ===
if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8002)
